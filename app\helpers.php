<?php

declare(strict_types=1);

if (!function_exists('settings')) {
    /**
     * Get a platform setting value.
     */
    function settings(string $key, mixed $default = null): mixed
    {
        return app('settings')->get($key, $default, 'platform', null);
    }
}

if (!function_exists('user_setting')) {
    /**
     * Get a user-specific setting.
     */
    function user_setting(string $key, mixed $default = null, ?int $userId = null): mixed
    {
        $userId = $userId ?? auth()->id();
        return app('settings')->get($key, $default, 'user', $userId);
    }
}

if (!function_exists('company_setting')) {
    /**
     * Get a company-specific setting.
     */
    function company_setting(string $key, mixed $default = null, ?int $companyId = null): mixed
    {
        $companyId = $companyId ?? auth()->user()?->current_team_id;
        return app('settings')->get($key, $default, 'company', $companyId);
    }
}




