<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;
use App\Enums\UserType;

class SettingsPolicy
{
    /**
     * Determine if the user can access branding settings.
     * Only Independent Agents can access branding settings.
     */
    public function accessBrandingSettings(User $user): bool
    {
        return $user->user_type === UserType::INDEPENDENT_AGENT;
    }

    /**
     * Determine if the user can access company settings.
     * Only Brokerage Admins can access company settings.
     */
    public function accessCompanySettings(User $user): bool
    {
        return $user->user_type === UserType::BROKERAGE_ADMIN;
    }

    /**
     * Determine if the user can access platform settings.
     * Only Platform Administrators can access platform settings.
     */
    public function accessPlatformSettings(User $user): bool
    {
        return $user->user_type === UserType::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Determine if the user can access personal preferences.
     * All authenticated users can access personal preferences.
     */
    public function accessPersonalPreferences(User $user): bool
    {
        return true;
    }
}
