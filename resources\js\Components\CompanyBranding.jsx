import { usePage } from '@inertiajs/react'
import { memo, useState } from 'react'
import { SidebarMenuButton } from '@/Components/shadcn/ui/sidebar'

// Simple SVG Logo Component
function CompanyLogo({ className = 'size-8' }) {
  return (
    <svg
      className={className}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Building/Real Estate Icon */}
      <rect x="20" y="30" width="60" height="50" fill="currentColor" opacity="0.1" rx="4" />
      <rect x="25" y="35" width="15" height="15" fill="currentColor" opacity="0.3" rx="2" />
      <rect x="45" y="35" width="15" height="15" fill="currentColor" opacity="0.3" rx="2" />
      <rect x="65" y="35" width="10" height="15" fill="currentColor" opacity="0.3" rx="2" />
      <rect x="25" y="55" width="15" height="15" fill="currentColor" opacity="0.3" rx="2" />
      <rect x="45" y="55" width="15" height="15" fill="currentColor" opacity="0.3" rx="2" />
      <rect x="65" y="55" width="10" height="15" fill="currentColor" opacity="0.3" rx="2" />

      {/* Main Building Structure */}
      <path
        d="M15 25 L50 10 L85 25 L85 85 L15 85 Z"
        fill="currentColor"
        opacity="0.8"
      />

      {/* Windows */}
      <rect x="25" y="30" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="42" y="30" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="59" y="30" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="25" y="45" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="42" y="45" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="59" y="45" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="25" y="60" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="42" y="60" width="8" height="8" fill="white" opacity="0.9" rx="1" />
      <rect x="59" y="60" width="8" height="8" fill="white" opacity="0.9" rx="1" />

      {/* Door */}
      <rect x="42" y="70" width="16" height="15" fill="white" opacity="0.9" rx="2" />
      <circle cx="52" cy="77" r="1" fill="currentColor" />
    </svg>
  )
}

export default memo(() => {
  const { auth, companyBranding } = usePage().props
  const [logoError, setLogoError] = useState(false)

  // Get company/team name based on user type
  const getCompanyName = () => {
    const user = auth.user

    // For platform administrators
    if (user.user_type === 'platform_administrator') {
      return 'MLS Platform'
    }

    // For brokerage admins and company agents - check company branding first
    if (user.current_team?.name) {
      // If company has custom branding, use that
      if (companyBranding?.company_name) {
        return companyBranding.company_name
      }
      return user.current_team.name
    }

    // For independent agents - check personal branding
    if (user.user_type === 'independent_agent') {
      // If agent has custom business name, use that
      if (companyBranding?.business_name) {
        return companyBranding.business_name
      }
      return `${user.name} Real Estate`
    }

    // Fallback
    return 'Real Estate MLS'
  }

  // Get subtitle based on user type
  const getSubtitle = () => {
    const user = auth.user

    switch (user.user_type) {
      case 'platform_administrator':
        return 'Platform Administration'
      case 'brokerage_admin':
        return 'Brokerage Management'
      case 'independent_agent':
        return 'Independent Agent'
      case 'company_agent':
        return user.current_team?.name ? `Agent at ${user.current_team.name}` : 'Company Agent'
      default:
        return 'Real Estate Professional'
    }
  }

  // Get logo - either custom or default
  const getLogo = () => {
    // Check if there's a custom logo URL and no error
    if (companyBranding?.logo_url && !logoError) {
      return (
        <img
          src={companyBranding.logo_url}
          alt="Company Logo"
          className="size-8 object-contain rounded"
          onError={() => setLogoError(true)}
        />
      )
    }

    // Default SVG logo
    return <CompanyLogo />
  }

  // Get brand color or default
  const getBrandColor = () => {
    return companyBranding?.brand_color || '#3b82f6'
  }

  return (
    <SidebarMenuButton size="lg" className="cursor-default hover:bg-transparent">
      <div
        className="flex aspect-square size-8 items-center justify-center rounded-lg text-white"
        style={{ backgroundColor: getBrandColor() }}
      >
        {getLogo()}
      </div>
      <div className="grid flex-1 text-left text-sm leading-tight">
        <span className="truncate font-semibold text-sidebar-foreground">
          {getCompanyName()}
        </span>
        <span className="truncate text-xs text-sidebar-muted-foreground">
          {getSubtitle()}
        </span>
      </div>
    </SidebarMenuButton>
  )
})
