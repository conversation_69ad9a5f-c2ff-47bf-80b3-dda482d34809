import { Head, <PERSON> } from '@inertiajs/react'
import axios from 'axios'
import { Plus } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Button } from '@/Components/shadcn/ui/button'
import { ShadcnDataTable } from '@/Components/shadcn/ui/shadcn-data-table'
import AppLayout from '@/Layouts/AppLayout'
import { columns } from './columns'

export default function PlansIndex() {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await axios.get(route('admin.plans.api'))
      setData(response.data.data || [])
    }
    catch (error) {
      console.error('Error fetching plans:', error)
    }
    finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AppLayout title="Plan Management">
        <Head title="Plans" />
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Plan Management</h1>
              <p className="text-muted-foreground">
                Manage subscription plans and pricing
              </p>
            </div>
          </div>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading plans...</p>
            </div>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout title="Plan Management">
      <Head title="Plans" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Plan Management</h1>
            <p className="text-muted-foreground">
              Manage subscription plans and pricing
            </p>
          </div>
        </div>

        {/* DataTable */}
        <ShadcnDataTable
          columns={columns}
          data={data}
          filterableColumns={[
            {
              id: 'is_active',
              title: 'Status',
              options: [
                { label: 'Active', value: 'true' },
                { label: 'Inactive', value: 'false' },
              ],
            },
            {
              id: 'billing_period',
              title: 'Billing Period',
              options: [
                { label: 'Monthly', value: 'monthly' },
                { label: 'Yearly', value: 'yearly' },
                { label: 'One-time', value: 'one_time' },
              ],
            },
          ]}
          addButton={(
            <Link href={route('admin.plans.create')}>
              <Button size="sm" className="h-8">
                <Plus className="h-4 w-4 mr-2" />
                Add Plan
              </Button>
            </Link>
          )}
        />
      </div>
    </AppLayout>
  )
}
