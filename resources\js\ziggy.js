const Ziggy = { url: 'http:\/\/localhost:8000', port: 8000, defaults: {}, routes: { 'scribe': { uri: 'docs', methods: ['GET', 'HEAD'] }, 'scribe.postman': { uri: 'docs.postman', methods: ['GET', 'HEAD'] }, 'scribe.openapi': { uri: 'docs.openapi', methods: ['GET', 'HEAD'] }, 'cashier.payment': { uri: 'stripe\/payment\/{id}', methods: ['GET', 'HEAD'], parameters: ['id'] }, 'cashier.webhook': { uri: 'stripe\/webhook', methods: ['POST'] }, 'login': { uri: 'login', methods: ['GET', 'HEAD'] }, 'login.store': { uri: 'login', methods: ['POST'] }, 'logout': { uri: 'logout', methods: ['POST'] }, 'password.request': { uri: 'forgot-password', methods: ['GET', 'HEAD'] }, 'password.reset': { uri: 'reset-password\/{token}', methods: ['GET', 'HEAD'], parameters: ['token'] }, 'password.email': { uri: 'forgot-password', methods: ['POST'] }, 'password.update': { uri: 'reset-password', methods: ['POST'] }, 'register': { uri: 'register', methods: ['GET', 'HEAD'] }, 'register.store': { uri: 'register', methods: ['POST'] }, 'verification.notice': { uri: 'email\/verify', methods: ['GET', 'HEAD'] }, 'verification.verify': { uri: 'email\/verify\/{id}\/{hash}', methods: ['GET', 'HEAD'], parameters: ['id', 'hash'] }, 'verification.send': { uri: 'email\/verification-notification', methods: ['POST'] }, 'user-profile-information.update': { uri: 'user\/profile-information', methods: ['PUT'] }, 'user-password.update': { uri: 'user\/password', methods: ['PUT'] }, 'password.confirm': { uri: 'user\/confirm-password', methods: ['GET', 'HEAD'] }, 'password.confirmation': { uri: 'user\/confirmed-password-status', methods: ['GET', 'HEAD'] }, 'password.confirm.store': { uri: 'user\/confirm-password', methods: ['POST'] }, 'two-factor.login': { uri: 'two-factor-challenge', methods: ['GET', 'HEAD'] }, 'two-factor.login.store': { uri: 'two-factor-challenge', methods: ['POST'] }, 'two-factor.enable': { uri: 'user\/two-factor-authentication', methods: ['POST'] }, 'two-factor.confirm': { uri: 'user\/confirmed-two-factor-authentication', methods: ['POST'] }, 'two-factor.disable': { uri: 'user\/two-factor-authentication', methods: ['DELETE'] }, 'two-factor.qr-code': { uri: 'user\/two-factor-qr-code', methods: ['GET', 'HEAD'] }, 'two-factor.secret-key': { uri: 'user\/two-factor-secret-key', methods: ['GET', 'HEAD'] }, 'two-factor.recovery-codes': { uri: 'user\/two-factor-recovery-codes', methods: ['GET', 'HEAD'] }, 'two-factor.regenerate-recovery-codes': { uri: 'user\/two-factor-recovery-codes', methods: ['POST'] }, 'terms.show': { uri: 'terms-of-service', methods: ['GET', 'HEAD'] }, 'policy.show': { uri: 'privacy-policy', methods: ['GET', 'HEAD'] }, 'profile.show': { uri: 'user\/profile', methods: ['GET', 'HEAD'] }, 'other-browser-sessions.destroy': { uri: 'user\/other-browser-sessions', methods: ['DELETE'] }, 'current-user-photo.destroy': { uri: 'user\/profile-photo', methods: ['DELETE'] }, 'current-user.destroy': { uri: 'user', methods: ['DELETE'] }, 'api-tokens.index': { uri: 'user\/api-tokens', methods: ['GET', 'HEAD'] }, 'api-tokens.store': { uri: 'user\/api-tokens', methods: ['POST'] }, 'api-tokens.update': { uri: 'user\/api-tokens\/{token}', methods: ['PUT'], parameters: ['token'] }, 'api-tokens.destroy': { uri: 'user\/api-tokens\/{token}', methods: ['DELETE'], parameters: ['token'] }, 'sanctum.csrf-cookie': { uri: 'sanctum\/csrf-cookie', methods: ['GET', 'HEAD'] }, 'user.index': { uri: 'api\/user', methods: ['GET', 'HEAD'] }, 'user.store': { uri: 'api\/user', methods: ['POST'] }, 'user.show': { uri: 'api\/user\/{user}', methods: ['GET', 'HEAD'], parameters: ['user'], bindings: { user: 'id' } }, 'user.update': { uri: 'api\/user\/{user}', methods: ['PUT', 'PATCH'], parameters: ['user'], bindings: { user: 'id' } }, 'user.destroy': { uri: 'api\/user\/{user}', methods: ['DELETE'], parameters: ['user'], bindings: { user: 'id' } }, 'home': { uri: '\/', methods: ['GET', 'HEAD'] }, 'oauth.redirect': { uri: 'auth\/redirect\/{provider}', methods: ['GET', 'HEAD'], parameters: ['provider'] }, 'oauth.callback': { uri: 'auth\/callback\/{provider}', methods: ['GET', 'HEAD'], parameters: ['provider'] }, 'login-link.store': { uri: 'auth\/login-link', methods: ['POST'] }, 'login-link.login': { uri: 'auth\/login-link\/{token}', methods: ['GET', 'HEAD'], parameters: ['token'] }, 'dashboard': { uri: 'dashboard', methods: ['GET', 'HEAD'] }, 'oauth.destroy': { uri: 'auth\/destroy\/{provider}', methods: ['DELETE'], parameters: ['provider'] }, 'chat.index': { uri: 'chat', methods: ['GET', 'HEAD'] }, 'subscriptions.index': { uri: 'subscriptions', methods: ['GET', 'HEAD'] }, 'subscriptions.create': { uri: 'subscriptions\/create', methods: ['GET', 'HEAD'] }, 'subscriptions.store': { uri: 'subscriptions', methods: ['POST'] }, 'subscriptions.show': { uri: 'subscriptions\/{subscription}', methods: ['GET', 'HEAD'], parameters: ['subscription'] }, 'admin.users.index': { uri: 'admin\/users', methods: ['GET', 'HEAD'] }, 'admin.users.create': { uri: 'admin\/users\/create', methods: ['GET', 'HEAD'] }, 'admin.users.store': { uri: 'admin\/users', methods: ['POST'] }, 'admin.users.show': { uri: 'admin\/users\/{user}', methods: ['GET', 'HEAD'], parameters: ['user'], bindings: { user: 'id' } }, 'admin.users.edit': { uri: 'admin\/users\/{user}\/edit', methods: ['GET', 'HEAD'], parameters: ['user'], bindings: { user: 'id' } }, 'admin.users.update': { uri: 'admin\/users\/{user}', methods: ['PUT', 'PATCH'], parameters: ['user'], bindings: { user: 'id' } }, 'admin.users.destroy': { uri: 'admin\/users\/{user}', methods: ['DELETE'], parameters: ['user'], bindings: { user: 'id' } }, 'admin.users.api': { uri: 'admin\/users\/api\/data', methods: ['GET', 'HEAD'] }, 'admin.plans.index': { uri: 'admin\/plans', methods: ['GET', 'HEAD'] }, 'admin.plans.create': { uri: 'admin\/plans\/create', methods: ['GET', 'HEAD'] }, 'admin.plans.store': { uri: 'admin\/plans', methods: ['POST'] }, 'admin.plans.show': { uri: 'admin\/plans\/{plan}', methods: ['GET', 'HEAD'], parameters: ['plan'], bindings: { plan: 'id' } }, 'admin.plans.edit': { uri: 'admin\/plans\/{plan}\/edit', methods: ['GET', 'HEAD'], parameters: ['plan'], bindings: { plan: 'id' } }, 'admin.plans.update': { uri: 'admin\/plans\/{plan}', methods: ['PUT', 'PATCH'], parameters: ['plan'], bindings: { plan: 'id' } }, 'admin.plans.destroy': { uri: 'admin\/plans\/{plan}', methods: ['DELETE'], parameters: ['plan'], bindings: { plan: 'id' } }, 'admin.plans.api': { uri: 'admin\/plans\/api\/data', methods: ['GET', 'HEAD'] }, 'admin.media.index': { uri: 'admin\/media', methods: ['GET', 'HEAD'] }, 'admin.media.create': { uri: 'admin\/media\/create', methods: ['GET', 'HEAD'] }, 'admin.media.store': { uri: 'admin\/media', methods: ['POST'] }, 'admin.media.show': { uri: 'admin\/media\/{medium}', methods: ['GET', 'HEAD'], parameters: ['medium'] }, 'admin.media.edit': { uri: 'admin\/media\/{medium}\/edit', methods: ['GET', 'HEAD'], parameters: ['medium'] }, 'admin.media.update': { uri: 'admin\/media\/{medium}', methods: ['PUT', 'PATCH'], parameters: ['medium'] }, 'admin.media.destroy': { uri: 'admin\/media\/{medium}', methods: ['DELETE'], parameters: ['medium'] }, 'admin.media-folders.index': { uri: 'admin\/media-folders', methods: ['GET', 'HEAD'] }, 'admin.media-folders.create': { uri: 'admin\/media-folders\/create', methods: ['GET', 'HEAD'] }, 'admin.media-folders.store': { uri: 'admin\/media-folders', methods: ['POST'] }, 'admin.media-folders.show': { uri: 'admin\/media-folders\/{media_folder}', methods: ['GET', 'HEAD'], parameters: ['media_folder'] }, 'admin.media-folders.edit': { uri: 'admin\/media-folders\/{media_folder}\/edit', methods: ['GET', 'HEAD'], parameters: ['media_folder'] }, 'admin.media-folders.update': { uri: 'admin\/media-folders\/{media_folder}', methods: ['PUT', 'PATCH'], parameters: ['media_folder'] }, 'admin.media-folders.destroy': { uri: 'admin\/media-folders\/{media_folder}', methods: ['DELETE'], parameters: ['media_folder'] }, 'admin.media.bulk-destroy': { uri: 'admin\/media\/bulk-destroy', methods: ['POST'] }, 'admin.media.move-to-folder': { uri: 'admin\/media\/move-to-folder', methods: ['POST'] }, 'admin.settings.index': { uri: 'admin\/settings', methods: ['GET', 'HEAD'] }, 'admin.settings.general': { uri: 'admin\/settings\/general', methods: ['GET', 'HEAD'] }, 'admin.settings.general.update': { uri: 'admin\/settings\/general', methods: ['POST'] }, 'admin.settings.appearance': { uri: 'admin\/settings\/appearance', methods: ['GET', 'HEAD'] }, 'admin.settings.appearance.update': { uri: 'admin\/settings\/appearance', methods: ['POST'] }, 'admin.settings.notifications': { uri: 'admin\/settings\/notifications', methods: ['GET', 'HEAD'] }, 'admin.settings.notifications.update': { uri: 'admin\/settings\/notifications', methods: ['POST'] }, 'admin.settings.security': { uri: 'admin\/settings\/security', methods: ['GET', 'HEAD'] }, 'admin.settings.security.update': { uri: 'admin\/settings\/security', methods: ['POST'] }, 'admin.settings.store': { uri: 'admin\/settings', methods: ['POST'] }, 'admin.settings.update': { uri: 'admin\/settings\/{key}', methods: ['PUT'], parameters: ['key'] }, 'admin.settings.destroy': { uri: 'admin\/settings\/{key}', methods: ['DELETE'], parameters: ['key'] }, 'admin.settings.bulk-update': { uri: 'admin\/settings\/bulk-update', methods: ['POST'] }, 'admin.settings.api': { uri: 'admin\/settings\/api\/data', methods: ['GET', 'HEAD'] }, 'company.agents.index': { uri: 'company\/agents', methods: ['GET', 'HEAD'] }, 'company.agents.create': { uri: 'company\/agents\/create', methods: ['GET', 'HEAD'] }, 'company.agents.store': { uri: 'company\/agents', methods: ['POST'] }, 'company.agents.show': { uri: 'company\/agents\/{agent}', methods: ['GET', 'HEAD'], parameters: ['agent'], bindings: { agent: 'id' } }, 'company.agents.edit': { uri: 'company\/agents\/{agent}\/edit', methods: ['GET', 'HEAD'], parameters: ['agent'], bindings: { agent: 'id' } }, 'company.agents.update': { uri: 'company\/agents\/{agent}', methods: ['PUT', 'PATCH'], parameters: ['agent'], bindings: { agent: 'id' } }, 'company.agents.destroy': { uri: 'company\/agents\/{agent}', methods: ['DELETE'], parameters: ['agent'], bindings: { agent: 'id' } }, 'company.media.index': { uri: 'company\/media', methods: ['GET', 'HEAD'] }, 'company.media.create': { uri: 'company\/media\/create', methods: ['GET', 'HEAD'] }, 'company.media.store': { uri: 'company\/media', methods: ['POST'] }, 'company.media.show': { uri: 'company\/media\/{medium}', methods: ['GET', 'HEAD'], parameters: ['medium'] }, 'company.media.edit': { uri: 'company\/media\/{medium}\/edit', methods: ['GET', 'HEAD'], parameters: ['medium'] }, 'company.media.update': { uri: 'company\/media\/{medium}', methods: ['PUT', 'PATCH'], parameters: ['medium'] }, 'company.media.destroy': { uri: 'company\/media\/{medium}', methods: ['DELETE'], parameters: ['medium'] }, 'company.media-folders.index': { uri: 'company\/media-folders', methods: ['GET', 'HEAD'] }, 'company.media-folders.create': { uri: 'company\/media-folders\/create', methods: ['GET', 'HEAD'] }, 'company.media-folders.store': { uri: 'company\/media-folders', methods: ['POST'] }, 'company.media-folders.show': { uri: 'company\/media-folders\/{media_folder}', methods: ['GET', 'HEAD'], parameters: ['media_folder'] }, 'company.media-folders.edit': { uri: 'company\/media-folders\/{media_folder}\/edit', methods: ['GET', 'HEAD'], parameters: ['media_folder'] }, 'company.media-folders.update': { uri: 'company\/media-folders\/{media_folder}', methods: ['PUT', 'PATCH'], parameters: ['media_folder'] }, 'company.media-folders.destroy': { uri: 'company\/media-folders\/{media_folder}', methods: ['DELETE'], parameters: ['media_folder'] }, 'company.media.bulk-destroy': { uri: 'company\/media\/bulk-destroy', methods: ['POST'] }, 'company.media.move-to-folder': { uri: 'company\/media\/move-to-folder', methods: ['POST'] }, 'company.settings.index': { uri: 'company\/settings', methods: ['GET', 'HEAD'] }, 'company.settings.branding': { uri: 'company\/settings\/branding', methods: ['GET', 'HEAD'] }, 'company.settings.branding.update': { uri: 'company\/settings\/branding', methods: ['POST'] }, 'company.settings.preferences': { uri: 'company\/settings\/preferences', methods: ['GET', 'HEAD'] }, 'company.settings.preferences.update': { uri: 'company\/settings\/preferences', methods: ['POST'] }, 'agent.settings.index': { uri: 'agent\/settings', methods: ['GET', 'HEAD'] }, 'agent.settings.branding': { uri: 'agent\/settings\/branding', methods: ['GET', 'HEAD'] }, 'agent.settings.branding.update': { uri: 'agent\/settings\/branding', methods: ['POST'] }, 'agent.settings.preferences': { uri: 'agent\/settings\/preferences', methods: ['GET', 'HEAD'] }, 'agent.settings.preferences.update': { uri: 'agent\/settings\/preferences', methods: ['POST'] }, 'company-agent.settings.index': { uri: 'company-agent\/settings', methods: ['GET', 'HEAD'] }, 'company-agent.settings.preferences': { uri: 'company-agent\/settings\/preferences', methods: ['GET', 'HEAD'] }, 'company-agent.settings.preferences.update': { uri: 'company-agent\/settings\/preferences', methods: ['POST'] }, 'storage.local': { uri: 'storage\/{path}', methods: ['GET', 'HEAD'], wheres: { path: '.*' }, parameters: ['path'] } } }
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes)
}
export { Ziggy }
