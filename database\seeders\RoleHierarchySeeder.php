<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
use App\Models\Team;
use App\Enums\UserType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class RoleHierarchySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure roles are created first
        $this->call(RolePermissionSeeder::class);

        // Create Platform Administrator
        $this->createPlatformAdministrator();

        // Create Brokerage Admin with company
        $this->createBrokerageAdmin();

        // Create Independent Agent
        $this->createIndependentAgent();

        // Create Company Agent under the brokerage
        $this->createCompanyAgent();
    }

    private function createPlatformAdministrator(): void
    {
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Platform Administrator',
                'password' => Hash::make('password'),
                'user_type' => UserType::PLATFORM_ADMINISTRATOR,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => true,
            ]
        );

        // Assign role
        $role = Role::where('name', UserType::PLATFORM_ADMINISTRATOR->value)->first();
        if ($role) {
            $user->assignRole($role);
        }

        $this->command->info("Created Platform Administrator: {$user->email}");
    }

    private function createBrokerageAdmin(): void
    {
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Brokerage Administrator',
                'password' => Hash::make('password'),
                'user_type' => UserType::BROKERAGE_ADMIN,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => true,
            ]
        );

        // Create a brokerage/company team with the user as owner
        $brokerageTeam = Team::firstOrCreate(
            ['name' => 'Elite Realty Brokerage'],
            [
                'personal_team' => false,
                'user_id' => $user->id,
            ]
        );

        // Update user's current team
        $user->update(['current_team_id' => $brokerageTeam->id]);

        // Add user to team
        $brokerageTeam->users()->syncWithoutDetaching([$user->id]);

        // Assign role
        $role = Role::where('name', UserType::BROKERAGE_ADMIN->value)->first();
        if ($role) {
            $user->assignRole($role);
        }

        $this->command->info("Created Brokerage Admin: {$user->email} with company: {$brokerageTeam->name}");
    }

    private function createIndependentAgent(): void
    {
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Smith',
                'password' => Hash::make('password'),
                'user_type' => UserType::INDEPENDENT_AGENT,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => false,
                'license_number' => 'RE123456',
                'license_state' => 'CA',
                'phone' => '******-0123',
            ]
        );

        // Create personal team for independent agent
        $personalTeam = Team::firstOrCreate(
            ['name' => 'John Smith Real Estate'],
            [
                'personal_team' => true,
                'user_id' => $user->id,
            ]
        );

        // Update user's current team
        $user->update(['current_team_id' => $personalTeam->id]);

        // Add user to team
        $personalTeam->users()->syncWithoutDetaching([$user->id]);

        // Assign role
        $role = Role::where('name', UserType::INDEPENDENT_AGENT->value)->first();
        if ($role) {
            $user->assignRole($role);
        }

        $this->command->info("Created Independent Agent: {$user->email} with personal team: {$personalTeam->name}");
    }

    private function createCompanyAgent(): void
    {
        // Get the brokerage team
        $brokerageTeam = Team::where('name', 'Elite Realty Brokerage')->first();

        if (!$brokerageTeam) {
            $this->command->error('Brokerage team not found. Please run brokerage admin creation first.');
            return;
        }

        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sarah Johnson',
                'password' => Hash::make('password'),
                'user_type' => UserType::COMPANY_AGENT,
                'email_verified_at' => now(),
                'current_team_id' => $brokerageTeam->id,
                'parent_company_id' => $brokerageTeam->id,
                'can_upload_media' => true,
                'can_access_company_media' => true,
                'license_number' => 'RE789012',
                'license_state' => 'CA',
                'phone' => '******-0456',
            ]
        );

        // Add user to brokerage team with role
        $brokerageTeam->users()->syncWithoutDetaching([$user->id => ['role' => 'agent']]);

        // Assign role
        $role = Role::where('name', UserType::COMPANY_AGENT->value)->first();
        if ($role) {
            $user->assignRole($role);
        }

        $this->command->info("Created Company Agent: {$user->email} under brokerage: {$brokerageTeam->name}");
    }
}
