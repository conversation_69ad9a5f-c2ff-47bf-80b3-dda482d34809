import { usePage } from '@inertiajs/react'
import axios from 'axios'
import {
  FileIcon,
  FileTextIcon,
  Folder,
  FolderPlus,
  ImageIcon,
  MusicIcon,
  Search,
  Upload,
  VideoIcon,
} from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useToast } from '@/Components/hooks/use-toast'
import MediaPickerLibrary from '@/Components/MediaPickerLibrary'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent } from '@/Components/shadcn/ui/card'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/Components/shadcn/ui/dialog'
import { Input } from '@/Components/shadcn/ui/input'

import CreateFolderModal from '@/Pages/Admin/Media/Components/CreateFolderModal'
// Import existing components
import MediaUploader from '@/Pages/Admin/Media/Components/MediaUploader'

/**
 * MediaPickerModal component for selecting media files
 *
 * @param {object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Callback when modal is closed
 * @param {Function} props.onSelect - Callback when media is selected
 * @param {object} props.filters - Filters to apply (e.g., {type: 'image'})
 * @param {string} props.title - Modal title
 */
export default function MediaPickerModal({
  isOpen,
  onClose,
  onSelect,
  filters = {},
  title = 'Select Media',
}) {
  const { toast } = useToast()
  const { props: { auth } } = usePage()

  const [loading, setLoading] = useState(false)
  const [media, setMedia] = useState({ data: [], current_page: 1, last_page: 1 })
  const [folders, setFolders] = useState([])
  const [currentFolder, setCurrentFolder] = useState(null)
  const [breadcrumbs, setBreadcrumbs] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState('grid')
  const [selectedMedia, setSelectedMedia] = useState(null)

  // Modal states
  const [showUploader, setShowUploader] = useState(false)
  const [showCreateFolder, setShowCreateFolder] = useState(false)

  // Load media data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadMediaData()
    }
  }, [isOpen, currentFolder, searchQuery, filters])

  const loadMediaData = async () => {
    setLoading(true)
    try {
      const params = {
        folder_id: currentFolder?.id || null,
        search: searchQuery,
        modal: 1, // Indicate this is a modal request
        ...filters,
      }

      // Determine the correct route based on user context
      const isPlatformAdmin = auth.user.user_type === 'platform_administrator'
      const isCompanyContext = auth.user.user_type === 'brokerage_admin'
        || auth.user.user_type === 'independent_agent'
        || auth.user.user_type === 'company_agent'

      const baseRoute = isPlatformAdmin
        ? '/admin/media'
        : (isCompanyContext ? '/company/media' : '/admin/media')

      const response = await axios.get(baseRoute, { params })

      setMedia(response.data.media || { data: [], current_page: 1, last_page: 1 })
      setFolders(response.data.folders || [])
      setBreadcrumbs(response.data.breadcrumbs || [])
    }
    catch (error) {
      console.error('Failed to load media:', error)
      toast({
        title: 'Error',
        description: 'Failed to load media files',
        variant: 'destructive',
      })
    }
    finally {
      setLoading(false)
    }
  }

  const handleFolderClick = (folder) => {
    setCurrentFolder(folder)
    setSelectedMedia(null)
  }

  const handleBreadcrumbClick = (folder) => {
    setCurrentFolder(folder)
    setSelectedMedia(null)
  }

  const handleMediaClick = (mediaItem) => {
    setSelectedMedia(mediaItem)
  }

  const handleSelect = () => {
    if (selectedMedia) {
      onSelect(selectedMedia)
    }
  }

  const handleUploadComplete = () => {
    setShowUploader(false)
    loadMediaData()
    toast({
      title: 'Success',
      description: 'Files uploaded successfully',
    })
  }

  const handleFolderCreated = () => {
    setShowCreateFolder(false)
    loadMediaData()
  }

  const handleLoadMore = async () => {
    if (loading || media.current_page >= media.last_page)
      return

    try {
      const params = {
        folder_id: currentFolder?.id || null,
        search: searchQuery,
        modal: 1,
        page: media.current_page + 1,
        ...filters,
      }

      const isPlatformAdmin = auth.user.user_type === 'platform_administrator'
      const isCompanyContext = auth.user.user_type === 'brokerage_admin'
        || auth.user.user_type === 'independent_agent'
        || auth.user.user_type === 'company_agent'

      const baseRoute = isPlatformAdmin
        ? '/admin/media'
        : (isCompanyContext ? '/company/media' : '/admin/media')

      const response = await axios.get(baseRoute, { params })

      // Append new media to existing media
      setMedia(prevMedia => ({
        ...response.data.media,
        data: [...prevMedia.data, ...response.data.media.data],
      }))
    }
    catch (error) {
      console.error('Failed to load more media:', error)
      toast({
        title: 'Error',
        description: 'Failed to load more files',
        variant: 'destructive',
      })
    }
  }

  const getFileIcon = (mediaItem) => {
    if (mediaItem.file_type === 'image') {
      return <ImageIcon className="h-5 w-5 text-blue-500" />
    }
    else if (mediaItem.file_type === 'video') {
      return <VideoIcon className="h-5 w-5 text-purple-500" />
    }
    else if (mediaItem.file_type === 'audio') {
      return <MusicIcon className="h-5 w-5 text-green-500" />
    }
    else if (mediaItem.file_type === 'document') {
      return <FileTextIcon className="h-5 w-5 text-orange-500" />
    }
    else {
      return <FileIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const renderMediaGrid = () => (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {/* Folders */}
      {folders.map(folder => (
        <Card
          key={`folder-${folder.id}`}
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => handleFolderClick(folder)}
        >
          <CardContent className="p-4 text-center">
            <Folder className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <p className="text-xs font-medium truncate">{folder.name}</p>
            <p className="text-xs text-muted-foreground">
              {folder.media_count}
              {' '}
              files
            </p>
          </CardContent>
        </Card>
      ))}

      {/* Media Files */}
      {media.map(mediaItem => (
        <Card
          key={`media-${mediaItem.id}`}
          className={`cursor-pointer hover:shadow-md transition-shadow ${
            selectedMedia?.id === mediaItem.id ? 'ring-2 ring-primary' : ''
          }`}
          onClick={() => handleMediaClick(mediaItem)}
        >
          <CardContent className="p-2">
            {mediaItem.file_type === 'image' && mediaItem.thumbnail_url
              ? (
                  <img
                    src={mediaItem.thumbnail_url}
                    alt={mediaItem.alt_text || mediaItem.original_filename}
                    className="w-full h-20 object-cover rounded mb-2"
                  />
                )
              : (
                  <div className="w-full h-20 bg-muted rounded mb-2 flex items-center justify-center">
                    {getFileIcon(mediaItem)}
                  </div>
                )}
            <p className="text-xs font-medium truncate">
              {mediaItem.title || mediaItem.original_filename}
            </p>
            <div className="flex items-center justify-between mt-1">
              <Badge variant="secondary" className="text-xs">
                {mediaItem.file_type}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {mediaItem.formatted_size}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-6xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>
              Browse and select media files from your library, or upload new ones.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Toolbar */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {/* Breadcrumbs */}
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleBreadcrumbClick(null)}
                    className="text-xs"
                  >
                    Home
                  </Button>
                  {breadcrumbs.map((crumb, index) => (
                    <React.Fragment key={crumb.id}>
                      <span className="text-muted-foreground">/</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleBreadcrumbClick(crumb)}
                        className="text-xs"
                      >
                        {crumb.name}
                      </Button>
                    </React.Fragment>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search media..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="pl-8 w-64"
                  />
                </div>

                {/* Actions */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCreateFolder(true)}
                >
                  <FolderPlus className="h-4 w-4 mr-2" />
                  Folder
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowUploader(true)}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                </Button>
              </div>
            </div>

            {/* Content */}
            <MediaPickerLibrary
              folders={folders}
              media={media}
              selectedMedia={selectedMedia}
              onMediaSelect={setSelectedMedia}
              onFolderClick={handleFolderClick}
              loading={loading}
              onLoadMore={handleLoadMore}
              hasMore={media.current_page < media.last_page}
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSelect}
              disabled={!selectedMedia}
            >
              Select
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Sub-modals */}
      <MediaUploader
        isOpen={showUploader}
        onClose={() => setShowUploader(false)}
        onUploadComplete={handleUploadComplete}
        currentFolder={currentFolder}
        uploadRoute={auth.user.user_type === 'platform_administrator' ? route('admin.media.store') : undefined}
      />

      <CreateFolderModal
        isOpen={showCreateFolder}
        onClose={() => setShowCreateFolder(false)}
        currentFolder={currentFolder}
        onFolderCreated={handleFolderCreated}
        isAdminContext={auth.user.user_type === 'platform_administrator'}
      />
    </>
  )
}
