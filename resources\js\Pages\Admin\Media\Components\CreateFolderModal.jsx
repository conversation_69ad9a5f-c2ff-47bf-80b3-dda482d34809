import { router } from '@inertiajs/react'
import axios from 'axios'
import { Folder, Info, Loader2 } from 'lucide-react'
import { useMemo, useState } from 'react'
import { useToast } from '@/Components/hooks/use-toast'
import { Alert, AlertDescription } from '@/Components/shadcn/ui/alert'
import { Button } from '@/Components/shadcn/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/Components/shadcn/ui/dialog'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { Textarea } from '@/Components/shadcn/ui/textarea'

export default function CreateFolderModal({
  isOpen,
  onClose,
  currentFolder = null,
  onFolderCreated,
  isAdminContext = null,
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState({})
  const { toast } = useToast()

  // Generate slug preview and validation
  const folderValidation = useMemo(() => {
    const name = formData.name.trim()

    if (!name) {
      return { isValid: false, slug: '', message: '' }
    }

    // Check for invalid characters
    const invalidChars = name.match(/[^\w\s\-.()]/g)
    if (invalidChars) {
      return {
        isValid: false,
        slug: '',
        message: `Invalid characters: ${[...new Set(invalidChars)].join(', ')}. Only letters, numbers, spaces, hyphens, underscores, dots, and parentheses are allowed.`,
      }
    }

    // Generate slug preview
    const slug = name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-_.()]/g, '')
      .replace(/-+/g, '-')
      .replace(/^-+|-+$/g, '')

    if (!slug) {
      return {
        isValid: false,
        slug: '',
        message: 'Folder name must contain at least one valid character.',
      }
    }

    return {
      isValid: true,
      slug,
      message: '',
    }
  }, [formData.name])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    try {
      // Use isAdminContext prop if provided, otherwise fall back to URL detection
      const isCompanyContext = isAdminContext !== null
        ? !isAdminContext
        : window.location.pathname.includes('/company/')
      const routeName = isCompanyContext ? 'company.media-folders.store' : 'admin.media-folders.store'

      const response = await axios.post(route(routeName), {
        ...formData,
        parent_id: currentFolder?.id || null,
      })

      if (response.data.success) {
        toast({
          title: 'Success',
          description: response.data.message,
        })

        // Reset form
        setFormData({
          name: '',
          description: '',
        })

        onClose()

        // Refresh the page to show new folder
        if (onFolderCreated) {
          onFolderCreated(response.data.folder)
        }
        else {
          router.reload()
        }
      }
    }
    catch (error) {
      if (error.response?.status === 422) {
        setErrors(error.response.data.errors || {})
      }
      else {
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to create folder',
          variant: 'destructive',
        })
      }
    }
    finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        name: '',
        description: '',
      })
      setErrors({})
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Folder className="w-5 h-5" />
            Create New Folder
          </DialogTitle>
          <DialogDescription>
            Create a new folder
            {' '}
            {currentFolder ? `inside "${currentFolder.name}"` : 'in the root directory'}
            .
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Folder Name */}
          <div className="space-y-2">
            <Label htmlFor="folder-name">Folder Name *</Label>
            <Input
              id="folder-name"
              value={formData.name}
              onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter folder name (e.g., My Documents)"
              disabled={isSubmitting}
              className={errors.name || !folderValidation.isValid && formData.name ? 'border-destructive' : ''}
            />

            {/* Validation Error */}
            {!folderValidation.isValid && formData.name && (
              <p className="text-sm text-destructive">{folderValidation.message}</p>
            )}

            {/* Server Error */}
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name[0]}</p>
            )}

            {/* Slug Preview */}
            {folderValidation.isValid && folderValidation.slug && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>Folder URL:</strong>
                  {' '}
                  <code className="bg-muted px-1 py-0.5 rounded text-sm">{folderValidation.slug}</code>
                  <br />
                  <span className="text-xs text-muted-foreground">
                    This is how the folder will appear in URLs and file paths.
                  </span>
                </AlertDescription>
              </Alert>
            )}

            <p className="text-xs text-muted-foreground">
              • Only letters, numbers, spaces, hyphens, underscores, dots, and parentheses are allowed
              <br />
              • If a folder with the same name exists, a number will be added automatically
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="folder-description">Description</Label>
            <Textarea
              id="folder-description"
              value={formData.description}
              onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Optional description for this folder"
              disabled={isSubmitting}
              rows={3}
              className={errors.description ? 'border-destructive' : ''}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description[0]}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !folderValidation.isValid}
            >
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Create Folder
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
