# Settings System Documentation

## Overview

The MLS application features a comprehensive multi-tier settings system that provides role-based configuration management. Each user type has access to their own isolated settings without inheritance, ensuring clean separation of concerns.

## Architecture

### Database Structure

The settings system uses a single `settings` table with scope-based isolation:

```sql
CREATE TABLE settings (
    id BIGINT PRIMARY KEY,
    key VARCHAR(255) NOT NULL,
    value TEXT,
    scope_type VARCHAR(255) DEFAULT 'platform',  -- platform, company, user
    scope_id BIGINT NULL,                        -- company_id or user_id
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE KEY unique_setting (key, scope_type, scope_id)
);
```

### Scope Types

1. **Platform Scope** (`platform`, `null`): Global platform settings
2. **Company Scope** (`company`, `company_id`): Company-specific settings
3. **User Scope** (`user`, `user_id`): Individual user settings

## User Types & Access

### 1. Platform Administrator (Super Admin)
- **Route**: `/admin/settings`
- **Scope**: `platform`
- **Access**: Full platform configuration

**Available Settings:**
- **General**: App name, description, timezone, maintenance mode
- **Appearance**: Theme settings, colors, layout preferences
- **Notifications**: Email configuration, alert settings
- **Security**: Password policies, session management, 2FA settings

### 2. Brokerage Admin
- **Route**: `/company/settings`
- **Scope**: `company` (scoped to their company)
- **Access**: Company branding and operational settings

**Available Settings:**
- **Company Branding**: 
  - Company name, logo URL, brand color
  - Company tagline, website URL
- **Company Preferences**:
  - Default listing status, approval requirements
  - Company timezone, max listings per agent

### 3. Independent Agent
- **Route**: `/agent/settings`
- **Scope**: `user` (scoped to their user ID)
- **Access**: Personal branding and preferences

**Available Settings:**
- **Personal Branding**:
  - Professional name, title, photo URL
  - Professional bio, personal website, contact phone
- **Personal Preferences**:
  - Preferred contact method, email notifications
  - Auto-renew listings, default listing duration

### 4. Company Agent
- **Route**: `/agent/settings`
- **Scope**: `user` (scoped to their user ID)
- **Access**: Limited personal preferences only

**Available Settings:**
- **Personal Preferences** (Limited):
  - Preferred contact method
  - Email notification preferences

## Technical Implementation

### Services

#### SettingsService
Core service handling all settings operations:

```php
class SettingsService
{
    // Get setting with scope
    public function get(string $key, mixed $default = null, string $scopeType = 'platform', ?int $scopeId = null): mixed
    
    // Set setting with scope
    public function set(string $key, mixed $value, string $scopeType = 'platform', ?int $scopeId = null): void
    
    // Get all settings for scope
    public function all(string $scopeType = 'platform', ?int $scopeId = null): array
    
    // Bulk update settings
    public function bulkUpdate(array $settings, string $scopeType = 'platform', ?int $scopeId = null): void
}
```

### Helper Functions

```php
// Platform settings
settings('app_name', 'Default App Name');

// Company settings
company_setting('company_name', 'Default Company', $companyId);

// User settings
user_setting('agent_name', 'John Doe', $userId);
```

### Controllers

Each user type has dedicated controllers:

- `Admin\SettingsController` - Platform Administrator
- `Admin\CompanySettingsController` - Brokerage Admin
- `Agent\AgentSettingsController` - Independent & Company Agents

### Routes

```php
// Platform Settings (Super Admin only)
Route::middleware('role:platform_admin')->prefix('admin')->name('admin.')->group(function () {
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::get('/settings/{section}', [SettingsController::class, '{section}'])->name('settings.{section}');
    Route::post('/settings/{section}', [SettingsController::class, 'update{Section}'])->name('settings.{section}.update');
});

// Company Settings (Brokerage Admin only)
Route::middleware('role:brokerage_admin')->prefix('company')->name('company.')->group(function () {
    Route::get('/settings', [CompanySettingsController::class, 'index'])->name('settings.index');
    Route::get('/settings/{section}', [CompanySettingsController::class, '{section}'])->name('settings.{section}');
    Route::post('/settings/{section}', [CompanySettingsController::class, 'update{Section}'])->name('settings.{section}.update');
});

// Agent Settings (Independent Agent & Company Agent)
Route::middleware('role:independent_agent,company_agent')->prefix('agent')->name('agent.')->group(function () {
    Route::get('/settings', [AgentSettingsController::class, 'index'])->name('settings.index');
    Route::get('/settings/{section}', [AgentSettingsController::class, '{section}'])->name('settings.{section}');
    Route::post('/settings/{section}', [AgentSettingsController::class, 'update{Section}'])->name('settings.{section}.update');
});
```

## Frontend Components

### Reusable Components

1. **Index.jsx**: Settings overview with grid layout
2. **Section.jsx**: Individual settings section with form handling

### Dynamic Route System

Components use dynamic route prefixes for flexibility:

```javascript
// Props passed from controllers
routePrefix: 'admin.settings'    // Platform Admin
routePrefix: 'company.settings'  // Brokerage Admin  
routePrefix: 'agent.settings'    // Independent/Company Agent

// Dynamic route generation
route(`${routePrefix}.index`)                    // Back to overview
route(`${routePrefix}.${sectionKey}`)           // Section page
route(`${routePrefix}.${sectionKey}.update`)    // Form submission
```

## Security & Access Control

### Middleware Protection

- **Platform Settings**: `role:platform_admin`
- **Company Settings**: `role:brokerage_admin`
- **Agent Settings**: `role:independent_agent,company_agent`

### Authorization Rules

- **Branding Settings**: Only Independent Agents can access personal branding
- **Company Settings**: Only accessible by users belonging to that company
- **Platform Settings**: Only Super Admins have access

### Data Isolation

- No inheritance between scopes
- Each user type has completely separate settings
- Company agents cannot access company-wide settings
- Settings are scoped by user/company ID for security

## Usage Examples

### Adding New Settings

1. **Define in Controller**:
```php
private function getSettingSections(): array
{
    return [
        'new_section' => [
            'title' => 'New Section',
            'description' => 'Description of new section',
            'settings' => [
                'new_setting' => [
                    'label' => 'New Setting',
                    'type' => 'text',
                    'description' => 'Setting description',
                    'default' => 'default_value',
                ],
            ],
        ],
    ];
}
```

2. **Add Route**:
```php
Route::get('/settings/new_section', [Controller::class, 'newSection'])->name('settings.new_section');
Route::post('/settings/new_section', [Controller::class, 'updateNewSection'])->name('settings.new_section.update');
```

3. **Access in Code**:
```php
$value = settings('new_setting', 'default');
```

### Supported Field Types

- `text`: Text input
- `textarea`: Multi-line text
- `select`: Dropdown with options
- `boolean`: Toggle switch
- `number`: Numeric input
- `color`: Color picker
- `email`: Email input
- `url`: URL input

## Caching

Settings are automatically cached for performance:
- Cache key format: `setting_{scopeType}_{scopeId}_{key}`
- Cache duration: 1 hour
- Automatic cache invalidation on updates

## Best Practices

1. **Always use appropriate scope** when accessing settings
2. **Provide sensible defaults** for all settings
3. **Use descriptive keys** following dot notation (e.g., `email.smtp.host`)
4. **Validate input** based on field type
5. **Clear cache** when updating settings programmatically
6. **Test access control** for each user type

## Troubleshooting

### Common Issues

1. **Route not found**: Check middleware and route prefix
2. **Settings not saving**: Verify scope_type and scope_id
3. **Access denied**: Check user role and middleware
4. **Cache issues**: Clear settings cache manually if needed

### Debug Commands

```bash
# Check settings table
php artisan tinker --execute="App\Models\Setting::all()"

# Clear settings cache
php artisan cache:forget "setting_platform_null_app_name"

# Check user roles
php artisan tinker --execute="User::find(1)->user_type"
```
