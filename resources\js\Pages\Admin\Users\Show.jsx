import { Icon } from '@iconify/react'
import { Head, <PERSON> } from '@inertiajs/react'
import { route } from 'ziggy-js'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import AppLayout from '@/Layouts/AppLayout'

export default function ShowUser({ user }) {
  const getUserTypeBadge = (user) => {
    const variants = {
      platform_administrator: 'destructive',
      brokerage_admin: 'default',
      independent_agent: 'secondary',
      company_agent: 'outline',
    }

    const labels = {
      platform_administrator: 'Platform Administrator',
      brokerage_admin: 'Brokerage Admin',
      independent_agent: 'Independent Agent',
      company_agent: 'Company Agent',
    }

    return (
      <Badge variant={variants[user.user_type] || 'outline'}>
        {labels[user.user_type] || user.user_type}
      </Badge>
    )
  }

  const formatDate = (date) => {
    if (!date)
      return 'Not set'
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <AppLayout
      title="User Details"
      renderHeader={() => (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              User Details
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              View user information and details
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Link href={route('admin.users.edit', user.id)}>
              <Button>
                <Icon icon="lucide:edit" className="mr-2 h-4 w-4" />
                Edit User
              </Button>
            </Link>
            <Link href={route('admin.users.index')}>
              <Button variant="outline">
                <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
                Back to Users
              </Button>
            </Link>
          </div>
        </div>
      )}
    >
      <Head title={`User: ${user.name}`} />

      <div className="py-12">
        <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Basic Information
                  {getUserTypeBadge(user)}
                </CardTitle>
                <CardDescription>User's basic details and contact information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Full Name</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{user.name}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Email Address</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{user.email}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Phone Number</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{user.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">User Type</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">
                      {user.user_type_label || 'Unknown'}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Email Verified</h4>
                    <p className="mt-1">
                      {user.email_verified_at
                        ? (
                            <Badge variant="default">Verified</Badge>
                          )
                        : (
                            <Badge variant="destructive">Not Verified</Badge>
                          )}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Member Since</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{formatDate(user.created_at)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Information (for Brokerage Admins) */}
            {user.user_type === 'brokerage_admin' && (
              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                  <CardDescription>Company details and business information</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Company Name</h4>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">{user.company_name || 'Not provided'}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Company Website</h4>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">{user.company_website || 'Not provided'}</p>
                    </div>
                    <div className="md:col-span-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">Company Address</h4>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">{user.company_address || 'Not provided'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* License Information */}
            <Card>
              <CardHeader>
                <CardTitle>License Information</CardTitle>
                <CardDescription>Real estate license details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">License Number</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{user.license_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">License State</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{user.license_state || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">License Expiry</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">
                      {user.license_expiry ? formatDate(user.license_expiry) : 'Not provided'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Teams feature disabled for real estate SaaS */}
            {/* Team Information section removed */}

            {/* Account Status */}
            <Card>
              <CardHeader>
                <CardTitle>Account Status</CardTitle>
                <CardDescription>Account settings and status information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Profile Completed</h4>
                    <p className="mt-1">
                      {user.profile_completed
                        ? (
                            <Badge variant="default">Completed</Badge>
                          )
                        : (
                            <Badge variant="secondary">Incomplete</Badge>
                          )}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Onboarding Status</h4>
                    <p className="mt-1">
                      {user.onboarding_completed_at
                        ? (
                            <Badge variant="default">Completed</Badge>
                          )
                        : (
                            <Badge variant="secondary">Pending</Badge>
                          )}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Last Updated</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{formatDate(user.updated_at)}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Account ID</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">
                      #
                      {user.id}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
