<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Get a setting value for specific scope.
     */
    public function get(string $key, mixed $default = null, string $scopeType = 'platform', ?int $scopeId = null): mixed
    {
        $cacheKey = "setting_{$scopeType}_{$scopeId}_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default, $scopeType, $scopeId) {
            $setting = Setting::where('key', $key)
                ->where('scope_type', $scopeType)
                ->where('scope_id', $scopeId)
                ->first();

            if (!$setting) {
                return $default;
            }

            // Try to decode JSON, otherwise return as string
            $decoded = json_decode($setting->value, true);
            return json_last_error() === JSON_ERROR_NONE ? $decoded : $setting->value;
        });
    }

    /**
     * Set a setting value for specific scope.
     */
    public function set(string $key, mixed $value, string $scopeType = 'platform', ?int $scopeId = null): void
    {
        $setting = Setting::where('key', $key)
            ->where('scope_type', $scopeType)
            ->where('scope_id', $scopeId)
            ->first();

        if (!$setting) {
            $setting = new Setting([
                'key' => $key,
                'scope_type' => $scopeType,
                'scope_id' => $scopeId,
            ]);
        }

        // Encode arrays/objects as JSON, otherwise store as string
        $setting->value = is_array($value) || is_object($value)
            ? json_encode($value)
            : (string) $value;

        $setting->save();

        // Clear cache
        Cache::forget("setting_{$scopeType}_{$scopeId}_{$key}");
    }

    /**
     * Check if a setting exists.
     */
    public function has(string $key): bool
    {
        return Setting::where('key', $key)->exists();
    }

    /**
     * Delete a setting.
     */
    public function forget(string $key): bool
    {
        $deleted = Setting::where('key', $key)->delete();

        if ($deleted) {
            Cache::forget("setting_{$key}");
        }

        return $deleted > 0;
    }

    /**
     * Get all settings for specific scope.
     */
    public function all(string $scopeType = 'platform', ?int $scopeId = null): array
    {
        $cacheKey = "all_settings_{$scopeType}_{$scopeId}";

        return Cache::remember($cacheKey, 3600, function () use ($scopeType, $scopeId) {
            return Setting::where('scope_type', $scopeType)
                ->where('scope_id', $scopeId)
                ->get()
                ->mapWithKeys(function ($setting) {
                    $decoded = json_decode($setting->value, true);
                    return [$setting->key => json_last_error() === JSON_ERROR_NONE ? $decoded : $setting->value];
                })
                ->toArray();
        });
    }

    /**
     * Bulk update settings for specific scope.
     */
    public function bulkUpdate(array $settings, string $scopeType = 'platform', ?int $scopeId = null): void
    {
        foreach ($settings as $key => $value) {
            $this->set($key, $value, $scopeType, $scopeId);
        }

        // Clear scope-specific cache
        Cache::forget("all_settings_{$scopeType}_{$scopeId}");
    }
}
