<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\User;
use App\Models\Team;
use App\Models\UserSetting;
use App\Enums\UserType;
use App\Services\SettingsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SettingsTest extends TestCase
{
    use RefreshDatabase;

    private SettingsService $settingsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->settingsService = app(SettingsService::class);
    }

    public function test_platform_admin_can_create_platform_settings(): void
    {
        $admin = User::factory()->create(['user_type' => UserType::PLATFORM_ADMINISTRATOR]);
        $this->actingAs($admin);

        $setting = $this->settingsService->setPlatformSetting(
            'test.setting',
            'test value',
            'string',
            ['category' => 'test', 'description' => 'Test setting']
        );

        $this->assertInstanceOf(UserSetting::class, $setting);
        $this->assertEquals('test.setting', $setting->key);
        $this->assertEquals('test value', $setting->getActualValue());
        $this->assertEquals('platform', $setting->scope);
    }

    public function test_non_admin_cannot_create_platform_settings(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Only platform administrators can set platform settings');

        $this->settingsService->setPlatformSetting('test.setting', 'test value');
    }

    public function test_user_can_create_personal_settings(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        $setting = $this->settingsService->set(
            'user.preference',
            'dark',
            'string',
            null,
            ['category' => 'appearance']
        );

        $this->assertInstanceOf(UserSetting::class, $setting);
        $this->assertEquals('user.preference', $setting->key);
        $this->assertEquals('dark', $setting->getActualValue());
        $this->assertEquals('user', $setting->scope);
        $this->assertEquals($user->id, $setting->user_id);
    }

    public function test_settings_helper_functions_work(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        // Test setting a value
        settings()->set('test.helper', 'helper value');
        
        // Test getting a value
        $value = settings('test.helper');
        $this->assertEquals('helper value', $value);

        // Test default value
        $defaultValue = settings('non.existent', 'default');
        $this->assertEquals('default', $defaultValue);
    }

    public function test_boolean_settings_work_correctly(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        $setting = $this->settingsService->set('test.boolean', true, 'boolean');
        $this->assertTrue($setting->getActualValue());

        $setting = $this->settingsService->set('test.boolean', false, 'boolean');
        $this->assertFalse($setting->getActualValue());
    }

    public function test_json_settings_work_correctly(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        $data = ['key1' => 'value1', 'key2' => ['nested' => 'value']];
        $setting = $this->settingsService->set('test.json', $data, 'json');
        
        $this->assertEquals($data, $setting->getActualValue());
    }

    public function test_settings_access_control(): void
    {
        $admin = User::factory()->create(['user_type' => UserType::PLATFORM_ADMINISTRATOR]);
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);

        // Create a platform setting as admin
        $this->actingAs($admin);
        $platformSetting = UserSetting::create([
            'user_id' => $admin->id,
            'key' => 'platform.test',
            'value' => 'platform value',
            'type' => 'string',
            'scope' => 'platform',
            'category' => 'test',
            'is_public' => true,
        ]);

        // Test admin can access
        $this->assertTrue($platformSetting->canBeAccessedBy($admin));
        $this->assertTrue($platformSetting->canBeModifiedBy($admin));

        // Test regular user can access public platform settings but not modify
        $this->assertTrue($platformSetting->canBeAccessedBy($user));
        $this->assertFalse($platformSetting->canBeModifiedBy($user));
    }

    public function test_company_settings_work(): void
    {
        $team = Team::factory()->create();
        $brokerageAdmin = User::factory()->create([
            'user_type' => UserType::BROKERAGE_ADMIN,
            'current_team_id' => $team->id,
        ]);
        
        $this->actingAs($brokerageAdmin);

        $setting = $this->settingsService->setCompanySetting(
            'company.test',
            'company value',
            'string',
            $team,
            null,
            ['category' => 'general']
        );

        $this->assertEquals('company.test', $setting->key);
        $this->assertEquals('company value', $setting->getActualValue());
        $this->assertEquals('company', $setting->scope);
        $this->assertEquals($team->id, $setting->company_id);
    }

    public function test_settings_by_category(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        // Create settings in different categories
        $this->settingsService->set('appearance.theme', 'dark', 'string', null, ['category' => 'appearance']);
        $this->settingsService->set('appearance.layout', 'grid', 'string', null, ['category' => 'appearance']);
        $this->settingsService->set('notifications.email', true, 'boolean', null, ['category' => 'notifications']);

        $appearanceSettings = $this->settingsService->getByCategory('appearance');
        
        $this->assertCount(2, $appearanceSettings);
        $this->assertEquals('dark', $appearanceSettings['appearance.theme']);
        $this->assertEquals('grid', $appearanceSettings['appearance.layout']);
    }

    public function test_settings_deletion(): void
    {
        $user = User::factory()->create(['user_type' => UserType::INDEPENDENT_AGENT]);
        $this->actingAs($user);

        $this->settingsService->set('test.delete', 'value');
        $this->assertTrue($this->settingsService->has('test.delete'));

        $this->settingsService->delete('test.delete');
        $this->assertFalse($this->settingsService->has('test.delete'));
    }
}
