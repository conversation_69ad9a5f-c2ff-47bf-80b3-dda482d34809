import { Icon } from '@iconify/react'
import { Head, <PERSON>, router } from '@inertiajs/react'
import { useState } from 'react'
import { toast } from 'sonner'
import { route } from 'ziggy-js'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { ConfirmDialog } from '@/Components/shadcn/ui/confirm-dialog'
import { Separator } from '@/Components/shadcn/ui/separator'
import AppLayout from '@/Layouts/AppLayout'

export default function ShowPlan({ plan }) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const handleDelete = () => {
    router.delete(route('admin.plans.destroy', plan.id), {
      onSuccess: () => {
        toast.success('Plan deleted successfully')
      },
      onError: () => {
        toast.error('Failed to delete plan')
      },
    })
  }

  return (
    <AppLayout title={`Plan: ${plan.name}`}>
      <Head title={plan.name} />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{plan.name}</h1>
            <p className="text-muted-foreground">
              Plan details and configuration
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={route('admin.plans.edit', plan.id)}>
                <Icon icon="lucide:edit" className="mr-2 h-4 w-4" />
                Edit Plan
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={route('admin.plans.index')}>
                <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
                Back to Plans
              </Link>
            </Button>
            <Button variant="destructive" onClick={() => setShowDeleteConfirm(true)}>
              <Icon icon="lucide:trash" className="mr-2 h-4 w-4" />
              Delete Plan
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Plan Name</h3>
                <div className="flex items-center gap-2 mt-1">
                  <p className="text-lg font-semibold">{plan.name}</p>
                  {plan.is_popular && (
                    <Badge variant="secondary">Popular</Badge>
                  )}
                </div>
              </div>

              {plan.description && (
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Description</h3>
                  <p className="mt-1">{plan.description}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Price</h3>
                  <p className="text-2xl font-bold mt-1">
                    $
                    {plan.price}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Billing Period</h3>
                  <Badge variant="outline" className="mt-1">
                    {plan.billing_period}
                  </Badge>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Status</h3>
                <Badge variant={plan.is_active ? 'default' : 'secondary'} className="mt-1">
                  {plan.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>

              {plan.stripe_price_id && (
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Stripe Price ID</h3>
                  <p className="mt-1 font-mono text-sm bg-muted px-2 py-1 rounded">
                    {plan.stripe_price_id}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Limits & Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Limits & Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Max Agents</h3>
                  <p className="text-lg font-semibold mt-1">
                    {plan.max_agents || 'Unlimited'}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-sm text-muted-foreground">Max Listings</h3>
                  <p className="text-lg font-semibold mt-1">
                    {plan.max_listings || 'Unlimited'}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Sort Order</h3>
                <p className="text-lg font-semibold mt-1">{plan.sort_order}</p>
              </div>

              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Plan Type</h3>
                <div className="flex gap-2 mt-1">
                  <Badge variant={plan.is_active ? 'default' : 'secondary'}>
                    {plan.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  {plan.is_popular && (
                    <Badge variant="secondary">Popular</Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features */}
        <Card>
          <CardHeader>
            <CardTitle>Plan Features</CardTitle>
          </CardHeader>
          <CardContent>
            {plan.features && plan.features.length > 0
              ? (
                  <div className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Icon icon="lucide:check" className="h-4 w-4 text-green-600" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                )
              : (
                  <div className="text-center py-8">
                    <Icon icon="lucide:list" className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-semibold">No features defined</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Add features to describe what this plan includes.
                    </p>
                    <div className="mt-6">
                      <Button asChild>
                        <Link href={route('admin.plans.edit', plan.id)}>
                          <Icon icon="lucide:edit" className="mr-2 h-4 w-4" />
                          Edit Plan
                        </Link>
                      </Button>
                    </div>
                  </div>
                )}
          </CardContent>
        </Card>

        {/* Metadata */}
        <Card>
          <CardHeader>
            <CardTitle>Metadata</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h3 className="font-medium text-muted-foreground">Created</h3>
                <p className="mt-1">
                  {new Date(plan.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </p>
              </div>
              <div>
                <h3 className="font-medium text-muted-foreground">Last Updated</h3>
                <p className="mt-1">
                  {new Date(plan.updated_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button asChild>
                <Link href={route('admin.plans.edit', plan.id)}>
                  <Icon icon="lucide:edit" className="mr-2 h-4 w-4" />
                  Edit Plan
                </Link>
              </Button>

              <Button variant="outline" asChild>
                <Link href={route('admin.plans.create')}>
                  <Icon icon="lucide:copy" className="mr-2 h-4 w-4" />
                  Duplicate Plan
                </Link>
              </Button>

              <Separator orientation="vertical" className="h-6" />

              <Button variant="destructive" onClick={() => setShowDeleteConfirm(true)}>
                <Icon icon="lucide:trash" className="mr-2 h-4 w-4" />
                Delete Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title="Delete Plan"
        description={`Are you sure you want to delete "${plan.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </AppLayout>
  )
}
