<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Agent\AgentSettingsController;

/*
|--------------------------------------------------------------------------
| Agent Routes
|--------------------------------------------------------------------------
|
| These routes are for both Independent Agents and Company Agents.
| Independent agents have more features (like branding), while company
| agents have limited access as they inherit from their company.
|
*/

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified', 'role:independent_agent'])
    ->prefix('agent')
    ->name('agent.')
    ->group(function () {
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [AgentSettingsController::class, 'index'])->name('index');
            Route::get('/branding', [AgentSettingsController::class, 'branding'])->name('branding');
            Route::post('/branding', [AgentSettingsController::class, 'updateBranding'])->name('branding.update');
            Route::get('/preferences', [AgentSettingsController::class, 'preferences'])->name('preferences');
            Route::post('/preferences', [AgentSettingsController::class, 'updatePreferences'])->name('preferences.update');
        });
    });

// ===================================================================
// COMPANY AGENT ROUTES
// ===================================================================
Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified', 'role:company_agent'])
    ->prefix('company-agent')
    ->name('company-agent.')
    ->group(function () {
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [AgentSettingsController::class, 'index'])->name('index');
            Route::get('/preferences', [AgentSettingsController::class, 'preferences'])->name('preferences');
            Route::post('/preferences', [AgentSettingsController::class, 'updatePreferences'])->name('preferences.update');
        });
    });
