<?php

declare(strict_types=1);

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class AgentSettingsController extends Controller
{
    public function __construct(
        private readonly SettingsService $settingsService
    ) {
        $this->middleware(['auth', 'verified']);
        $this->middleware('role:independent_agent,company_agent');
    }

    /**
     * Display agent settings overview.
     */
    public function index(): Response
    {
        $userType = auth()->user()->user_type->value;
        $sections = $this->getAgentSettingSections($userType);

        return Inertia::render('Admin/Settings/Index', [
            'sections' => $sections,
            'pageTitle' => $userType === 'independent_agent' ? 'Agent Settings' : 'Personal Settings',
            'pageDescription' => $userType === 'independent_agent'
                ? 'Manage your personal branding and preferences.'
                : 'Manage your personal preferences.',
            'routePrefix' => 'agent.settings',
        ]);
    }

    /**
     * Display agent branding settings (Independent Agent only).
     */
    public function branding(): Response
    {
        abort_unless(auth()->user()->user_type->value === 'independent_agent', 403);
        return $this->renderSection('branding', 'Personal Branding');
    }

    /**
     * Update agent branding settings (Independent Agent only).
     */
    public function updateBranding(Request $request): RedirectResponse
    {
        abort_unless(auth()->user()->user_type->value === 'independent_agent', 403);
        return $this->updateSection($request, 'branding');
    }

    /**
     * Display agent preferences settings.
     */
    public function preferences(): Response
    {
        return $this->renderSection('preferences', 'Personal Preferences');
    }

    /**
     * Update agent preferences settings.
     */
    public function updatePreferences(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'preferences');
    }

    /**
     * Render a specific settings section.
     */
    private function renderSection(string $sectionKey, string $title): Response
    {
        $userType = auth()->user()->user_type->value;
        $sections = $this->getAgentSettingSections($userType);
        $section = $sections[$sectionKey] ?? abort(404);

        // Get current values for this user
        $userId = auth()->id();
        foreach ($section['settings'] as $key => &$setting) {
            $setting['value'] = $this->settingsService->get(
                $key,
                $setting['default'] ?? null,
                'user',
                $userId
            );
        }

        return Inertia::render('Admin/Settings/Section', [
            'sectionKey' => $sectionKey,
            'section' => $section,
            'allSections' => $sections,
            'routePrefix' => 'agent.settings',
        ]);
    }

    /**
     * Update settings for a specific section.
     */
    private function updateSection(Request $request, string $sectionKey): RedirectResponse
    {
        $userType = auth()->user()->user_type->value;
        $sections = $this->getAgentSettingSections($userType);
        $section = $sections[$sectionKey] ?? abort(404);

        $userId = auth()->id();

        // Validate and update each setting
        foreach ($section['settings'] as $key => $setting) {
            if ($request->has($key)) {
                $value = $request->input($key);

                // Basic validation based on type
                if ($setting['type'] === 'boolean') {
                    $value = (bool) $value;
                } elseif ($setting['type'] === 'number') {
                    $value = (int) $value;
                }

                $this->settingsService->set($key, $value, 'user', $userId);
            }
        }

        return redirect()->back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Get agent-specific setting sections based on user type.
     */
    private function getAgentSettingSections(string $userType): array
    {
        $sections = [];

        // Independent agents get branding settings
        if ($userType === 'independent_agent') {
            $sections['branding'] = [
                'title' => 'Personal Branding',
                'description' => 'Customize your personal brand and professional identity.',
                'icon' => 'Palette',
                'color' => 'purple',
                'settings' => [
                    'agent_name' => [
                        'label' => 'Professional Name',
                        'type' => 'text',
                        'description' => 'Your professional display name',
                        'default' => '',
                    ],
                    'agent_title' => [
                        'label' => 'Professional Title',
                        'type' => 'text',
                        'description' => 'Your professional title (e.g., "Real Estate Agent", "Broker")',
                        'default' => 'Real Estate Agent',
                    ],
                    'agent_photo' => [
                        'label' => 'Profile Photo URL',
                        'type' => 'url',
                        'description' => 'URL to your professional headshot',
                        'default' => '',
                    ],
                    'agent_bio' => [
                        'label' => 'Professional Bio',
                        'type' => 'textarea',
                        'description' => 'A brief professional biography',
                        'default' => '',
                    ],
                    'agent_website' => [
                        'label' => 'Personal Website',
                        'type' => 'url',
                        'description' => 'Your personal or professional website',
                        'default' => '',
                    ],
                    'agent_phone' => [
                        'label' => 'Contact Phone',
                        'type' => 'text',
                        'description' => 'Your professional contact phone number',
                        'default' => '',
                    ],
                ],
            ];
        }

        // All agents get preferences (limited for company agents)
        $preferenceSettings = [
            'preferred_contact_method' => [
                'label' => 'Preferred Contact Method',
                'type' => 'select',
                'description' => 'How you prefer to be contacted',
                'options' => [
                    'email' => 'Email',
                    'phone' => 'Phone',
                    'text' => 'Text Message',
                ],
                'default' => 'email',
            ],
            'email_notifications' => [
                'label' => 'Email Notifications',
                'type' => 'boolean',
                'description' => 'Receive email notifications for important updates',
                'default' => true,
            ],
        ];

        // Independent agents get additional preferences
        if ($userType === 'independent_agent') {
            $preferenceSettings = array_merge($preferenceSettings, [
                'listing_auto_renewal' => [
                    'label' => 'Auto-Renew Listings',
                    'type' => 'boolean',
                    'description' => 'Automatically renew expired listings',
                    'default' => false,
                ],
                'default_listing_duration' => [
                    'label' => 'Default Listing Duration (days)',
                    'type' => 'number',
                    'description' => 'Default duration for new listings',
                    'default' => 90,
                ],
            ]);
        }

        $sections['preferences'] = [
            'title' => 'Personal Preferences',
            'description' => 'Configure your personal preferences and notification settings.',
            'icon' => 'Settings',
            'color' => 'blue',
            'settings' => $preferenceSettings,
        ];

        return $sections;
    }
}
