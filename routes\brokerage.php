<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\MediaController;
use App\Http\Controllers\Company\AgentController;
use App\Http\Controllers\Admin\MediaFolderController;
use App\Http\Controllers\Company\CompanySettingsController;

/*
|--------------------------------------------------------------------------
| Brokerage Admin Routes
|--------------------------------------------------------------------------
|
| These routes are for Brokerage Administrators (Company Admin).
| They can manage their company's agents and company-level settings.
|
*/

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified', 'role:brokerage_admin'])
    ->prefix('company')
    ->name('company.')
    ->group(function () {

        Route::resource('agents', AgentController::class);

        Route::resource('media', MediaController::class);
        Route::resource('media-folders', MediaFolderController::class);
        Route::post('media/bulk-destroy', [MediaController::class, 'bulkDestroy'])->name('media.bulk-destroy');
        Route::post('media/move-to-folder', [MediaController::class, 'moveToFolder'])->name('media.move-to-folder');

        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [CompanySettingsController::class, 'index'])->name('index');
            Route::get('/branding', [CompanySettingsController::class, 'branding'])->name('branding');
            Route::post('/branding', [CompanySettingsController::class, 'updateBranding'])->name('branding.update');
            Route::get('/preferences', [CompanySettingsController::class, 'preferences'])->name('preferences');
            Route::post('/preferences', [CompanySettingsController::class, 'updatePreferences'])->name('preferences.update');
        });

    });
