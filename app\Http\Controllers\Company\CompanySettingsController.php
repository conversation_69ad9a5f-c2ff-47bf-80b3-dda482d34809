<?php

declare(strict_types=1);

namespace App\Http\Controllers\Company;

use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\Request;
use App\Services\SettingsService;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;

class CompanySettingsController extends Controller
{
    public function __construct(
        private readonly SettingsService $settingsService
    ) {
        $this->middleware(['auth', 'verified']);
        $this->middleware('role:brokerage_admin');
    }

    /**
     * Display company settings overview.
     */
    public function index(): Response
    {
        $sections = $this->getCompanySettingSections();

        return Inertia::render('Admin/Settings/Index', [
            'sections' => $sections,
            'pageTitle' => 'Company Settings',
            'pageDescription' => 'Manage your company branding and preferences.',
            'routePrefix' => 'company.settings',
        ]);
    }

    /**
     * Display company branding settings.
     */
    public function branding(): Response
    {
        return $this->renderSection('branding', 'Company Branding');
    }

    /**
     * Update company branding settings.
     */
    public function updateBranding(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'branding');
    }

    /**
     * Display company preferences settings.
     */
    public function preferences(): Response
    {
        return $this->renderSection('preferences', 'Company Preferences');
    }

    /**
     * Update company preferences settings.
     */
    public function updatePreferences(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'preferences');
    }

    /**
     * Render a specific settings section.
     */
    private function renderSection(string $sectionKey, string $title): Response
    {
        $sections = $this->getCompanySettingSections();
        $section = $sections[$sectionKey] ?? abort(404);

        // Get current values for this company
        $companyId = auth()->user()->current_team_id;
        foreach ($section['settings'] as $key => &$setting) {
            $setting['value'] = $this->settingsService->get(
                $key,
                $setting['default'] ?? null,
                'company',
                $companyId
            );
        }

        return Inertia::render('Admin/Settings/Section', [
            'sectionKey' => $sectionKey,
            'section' => $section,
            'allSections' => $sections,
            'routePrefix' => 'company.settings',
        ]);
    }

    /**
     * Update settings for a specific section.
     */
    private function updateSection(Request $request, string $sectionKey): RedirectResponse
    {
        $sections = $this->getCompanySettingSections();
        $section = $sections[$sectionKey] ?? abort(404);

        $companyId = auth()->user()->current_team_id;

        // Validate and update each setting
        foreach ($section['settings'] as $key => $setting) {
            if ($request->has($key)) {
                $value = $request->input($key);

                // Basic validation based on type
                if ($setting['type'] === 'boolean') {
                    $value = (bool) $value;
                } elseif ($setting['type'] === 'number') {
                    $value = (int) $value;
                }

                $this->settingsService->set($key, $value, 'company', $companyId);
            }
        }

        return redirect()->back()->with('success', 'Company settings updated successfully.');
    }

    /**
     * Get company-specific setting sections.
     */
    private function getCompanySettingSections(): array
    {
        return [
            'branding' => [
                'title' => 'Company Branding',
                'description' => 'Customize your company\'s brand appearance and identity.',
                'icon' => 'Palette',
                'color' => 'purple',
                'settings' => [
                    'company_name' => [
                        'label' => 'Company Name',
                        'type' => 'text',
                        'description' => 'Your company or brokerage name',
                        'default' => '',
                    ],
                    'company_logo' => [
                        'label' => 'Company Logo URL',
                        'type' => 'url',
                        'description' => 'URL to your company logo image',
                        'default' => '',
                    ],
                    'company_color' => [
                        'label' => 'Brand Color',
                        'type' => 'color',
                        'description' => 'Primary brand color for your company',
                        'default' => '#3b82f6',
                    ],
                    'company_tagline' => [
                        'label' => 'Company Tagline',
                        'type' => 'text',
                        'description' => 'A short tagline or slogan for your company',
                        'default' => '',
                    ],
                    'company_website' => [
                        'label' => 'Company Website',
                        'type' => 'url',
                        'description' => 'Your company website URL',
                        'default' => '',
                    ],
                ],
            ],
            'preferences' => [
                'title' => 'Company Preferences',
                'description' => 'Configure company-wide preferences and defaults.',
                'icon' => 'Settings',
                'color' => 'blue',
                'settings' => [
                    'default_listing_status' => [
                        'label' => 'Default Listing Status',
                        'type' => 'select',
                        'description' => 'Default status for new listings',
                        'options' => [
                            'draft' => 'Draft',
                            'active' => 'Active',
                            'pending' => 'Pending Review',
                        ],
                        'default' => 'draft',
                    ],
                    'require_listing_approval' => [
                        'label' => 'Require Listing Approval',
                        'type' => 'boolean',
                        'description' => 'Require admin approval before listings go live',
                        'default' => true,
                    ],
                    'company_timezone' => [
                        'label' => 'Company Timezone',
                        'type' => 'select',
                        'description' => 'Default timezone for your company',
                        'options' => [
                            'America/New_York' => 'Eastern Time',
                            'America/Chicago' => 'Central Time',
                            'America/Denver' => 'Mountain Time',
                            'America/Los_Angeles' => 'Pacific Time',
                        ],
                        'default' => 'America/New_York',
                    ],
                    'max_listings_per_agent' => [
                        'label' => 'Max Listings Per Agent',
                        'type' => 'number',
                        'description' => 'Maximum number of active listings per agent (0 = unlimited)',
                        'default' => 0,
                    ],
                ],
            ],
        ];
    }
}
