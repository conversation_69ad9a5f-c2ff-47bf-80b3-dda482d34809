<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

final class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
         $this->call(RolePermissionSeeder::class);
         $this->call(RoleHierarchySeeder::class);
         $this->call(PlanSeeder::class);
         $this->call(SimpleSettingsSeeder::class);
    }
}
