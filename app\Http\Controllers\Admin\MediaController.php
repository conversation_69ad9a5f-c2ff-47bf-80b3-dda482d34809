<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;
use App\Models\Media;
use App\Models\MediaMetadata;
use App\Models\MediaFolder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class MediaController extends Controller
{
    use AuthorizesRequests;
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response|\Illuminate\Http\JsonResponse
    {
        $user = $request->user();

        // Get accessible media based on user permissions
        $query = $user->getAccessibleMedia();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('original_filename', 'like', "%{$search}%")
                  ->orWhere('title', 'like', "%{$search}%")
                  ->orWhere('alt_text', 'like', "%{$search}%");
            });
        }

        if ($request->filled('type') && $request->get('type') !== 'all') {
            $type = $request->get('type');
            $extensions = $this->getExtensionsByType($type);
            $query->whereIn('extension', $extensions);
        }

        if ($request->filled('company_id')) {
            $query->where('company_id', $request->get('company_id'));
        }

        // Get current folder and its contents
        $currentFolderId = $request->get('folder_id');
        $currentFolder = null;

        if ($currentFolderId) {
            $currentFolder = MediaFolder::findOrFail($currentFolderId);
            // Check if user can access this folder
            if (!$currentFolder->canBeAccessedBy($user)) {
                abort(403, 'You cannot access this folder.');
            }
        }

        // Filter media by folder BEFORE pagination
        if ($currentFolderId) {
            $query->where('folder_id', $currentFolderId);
        } else {
            $query->whereNull('folder_id'); // Root folder files
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $media = $query->with(['uploader', 'company', 'metadata'])
                      ->paginate($request->get('per_page', 20))
                      ->withQueryString();

        // Get folders in current directory
        $folders = MediaFolder::accessibleBy($user)
            ->where('parent_id', $currentFolderId)
            ->with(['creator', 'company'])
            ->withCount(['mediaFiles', 'subfolders'])
            ->orderBy('name')
            ->get();

        // Get breadcrumbs for current folder
        $breadcrumbs = [];
        if ($currentFolder) {
            $breadcrumbs = $currentFolder->getAncestors();
            $breadcrumbs[] = $currentFolder;
        }

        // Return JSON only for modal requests, Inertia for page requests
        if ($request->has('modal') || ($request->expectsJson() && $request->header('X-Requested-With') === 'XMLHttpRequest')) {
            return response()->json([
                'media' => $media,
                'folders' => $folders,
                'currentFolder' => $currentFolder,
                'breadcrumbs' => $breadcrumbs,
                'filters' => [
                    'search' => $request->get('search', ''),
                    'type' => $request->get('type', 'all'),
                    'company_id' => $request->get('company_id'),
                    'folder_id' => $currentFolderId,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                ],
                'userPermissions' => [
                    'canUpload' => $user->can_upload_media,
                    'canManageCompanyMedia' => $user->user_type->canManageCompanyMedia(),
                    'canAccessAllMedia' => $user->user_type->canAccessAllMedia(),
                ],
            ]);
        }

        return Inertia::render('Admin/Media/Index', [
            'media' => $media,
            'folders' => $folders,
            'currentFolder' => $currentFolder,
            'breadcrumbs' => $breadcrumbs,
            'filters' => [
                'search' => $request->get('search', ''),
                'type' => $request->get('type', 'all'),
                'company_id' => $request->get('company_id'),
                'folder_id' => $currentFolderId,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ],
            'userPermissions' => [
                'canUpload' => $user->can_upload_media,
                'canManageCompanyMedia' => $user->user_type->canManageCompanyMedia(),
                'canAccessAllMedia' => $user->user_type->canAccessAllMedia(),
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        // Create log file manually to ensure logging works
        file_put_contents(storage_path('logs/debug.log'),
            "[" . now() . "] MediaController::store called by user {$user->id} ({$user->user_type->value})\n",
            FILE_APPEND | LOCK_EX
        );

        // TEMPORARILY BYPASS AUTHORIZATION FOR DEBUGGING
        // $this->authorize('create', Media::class);

        try {
            Log::info('Media upload request received', [
                'files_count' => $request->hasFile('files') ? count($request->file('files')) : 0,
                'user_id' => $user->id,
                'request_data' => $request->except(['files'])
            ]);

            // Get supported extensions from config
            $supportedExtensions = config('media.supported_extensions', []);
            $extensionsRule = 'required|file|max:51200'; // 50MB max

            if (!empty($supportedExtensions)) {
                $extensionsRule .= '|mimes:' . implode(',', $supportedExtensions);
            }

            $validator = Validator::make($request->all(), [
                'files' => 'required|array|min:1',
                'files.*' => $extensionsRule,
                'company_id' => 'nullable|exists:teams,id',
                'folder_id' => 'nullable|exists:media_folders,id',
            ]);

            if ($validator->fails()) {
                Log::warning('Media upload validation failed', [
                    'errors' => $validator->errors()->toArray()
                ]);

                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
        } catch (\Exception $e) {
            Log::error('Media upload error in validation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }

        try {
            $uploadedFiles = [];
            $errors = [];

            Log::info('Processing files for upload', [
                'files_count' => count($request->file('files')),
                'user_id' => $user->id
            ]);

            foreach ($request->file('files') as $file) {
                try {
                    Log::info('Processing individual file', [
                        'filename' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType()
                    ]);

                    $media = $this->uploadFile($file, $user, $request);
                    $uploadedFiles[] = $media->load(['uploader', 'company', 'metadata']);

                    Log::info('File uploaded successfully', [
                        'media_id' => $media->id,
                        'filename' => $media->original_filename
                    ]);
                } catch (\Exception $e) {
                    Log::error('Individual file upload failed', [
                        'filename' => $file->getClientOriginalName(),
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    $errors[] = [
                        'filename' => $file->getClientOriginalName(),
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('Upload process completed', [
                'uploaded_count' => count($uploadedFiles),
                'errors_count' => count($errors)
            ]);

            return response()->json([
                'success' => true,
                'uploaded' => $uploadedFiles,
                'errors' => $errors,
            ]);
        } catch (\Exception $e) {
            Log::error('Media upload process failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Upload process failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($medium): JsonResponse
    {
        // Handle route model binding issue - load media manually
        $media = Media::findOrFail($medium);

        $this->authorize('view', $media);

        return response()->json([
            'media' => $media->load(['uploader', 'company', 'metadata'])
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $medium)
    {
        // Handle route model binding issue - load media manually
        $media = Media::findOrFail($medium);

        // Debug media object
        Log::info('MediaController::update - Media object debug', [
            'route_param' => $medium,
            'media_id' => $media->id,
            'media_uploaded_by' => $media->uploaded_by,
            'media_company_id' => $media->company_id,
            'media_path' => $media->path,
            'request_route_params' => $request->route()->parameters(),
        ]);

        $this->authorize('update', $media);

        $validator = Validator::make($request->all(), [
            'title' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'folder_id' => 'nullable|exists:media_folders,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $validatedData = $validator->validated();

        // Handle physical file move if folder_id changed
        if (isset($validatedData['folder_id']) && $validatedData['folder_id'] !== $media->folder_id) {
            $this->moveMediaFile($media, $validatedData['folder_id']);
        }

        $media->update($validatedData);

        // Return JSON for AJAX requests, redirect for form submissions
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Media updated successfully.',
                'media' => $media->fresh()
            ]);
        }

        return back()->with('success', 'Media updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $medium)
    {
        // Handle route model binding issue - load media manually
        $media = Media::findOrFail($medium);

        $this->authorize('delete', $media);

        try {
            $media->delete();

            // Return JSON for AJAX requests, redirect for form submissions
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Media deleted successfully.'
                ]);
            }

            return back()->with('success', 'Media deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to delete media', [
                'media_id' => $media->id,
                'error' => $e->getMessage()
            ]);

            // Return JSON for AJAX requests, redirect for form submissions
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete media.'
                ], 500);
            }

            return back()->with('error', 'Failed to delete media. Please try again.');
        }
    }

    /**
     * Bulk delete media files.
     */
    public function bulkDestroy(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'media_ids' => 'required|array|min:1',
            'media_ids.*' => 'required|integer|exists:media,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $mediaIds = $request->get('media_ids');
        $deleted = 0;
        $errors = [];

        foreach ($mediaIds as $mediaId) {
            try {
                $media = Media::findOrFail($mediaId);

                if ($user->can('delete', $media)) {
                    $media->delete();
                    $deleted++;
                } else {
                    $errors[] = "No permission to delete {$media->original_filename}";
                }
            } catch (\Exception $e) {
                $errors[] = "Failed to delete media ID {$mediaId}: " . $e->getMessage();
            }
        }

        return response()->json([
            'success' => true,
            'deleted' => $deleted,
            'errors' => $errors,
        ]);
    }

    /**
     * Move media files to a folder.
     */
    public function moveToFolder(Request $request): JsonResponse
    {
        $request->validate([
            'media_ids' => 'required|array',
            'media_ids.*' => 'exists:media,id',
            'folder_id' => 'nullable|exists:media_folders,id',
        ]);

        $user = $request->user();
        $mediaIds = $request->get('media_ids');
        $folderId = $request->get('folder_id');

        // Check if user can access the target folder
        if ($folderId) {
            $folder = MediaFolder::findOrFail($folderId);
            if (!$folder->canBeAccessedBy($user)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot move files to this folder.'
                ], 403);
            }
        }

        $movedCount = 0;
        foreach ($mediaIds as $mediaId) {
            $media = Media::findOrFail($mediaId);

            // Check if user can update this media
            if ($media->canBeAccessedBy($user)) {
                $media->update(['folder_id' => $folderId]);
                $movedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully moved {$movedCount} file(s).",
            'moved_count' => $movedCount
        ]);
    }

    /**
     * Upload a single file and create media record.
     */
    private function uploadFile($file, $user, $request): Media
    {
        $originalName = $file->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();
        $size = $file->getSize();

        // Generate unique filename
        $filename = Str::uuid() . '.' . $extension;

        // Get folder path
        $folderPath = $this->getStorageFolderPath($request);

        // Store file in folder
        $path = $file->storeAs($folderPath, $filename, 'public');

        // Create media record
        $media = Media::create([
            'filename' => $filename,
            'original_filename' => $originalName,
            'mime_type' => $mimeType,
            'extension' => $extension,
            'size' => $size,
            'disk' => 'public',
            'path' => $path,
            'uploaded_by' => $user->id,
            'company_id' => $user->user_type->canAccessAllMedia() ? null : ($request->get('company_id') ?: $user->currentTeam?->id),
            'folder_id' => $request->get('folder_id'),
            'title' => $request->get('title'),
            'alt_text' => $request->get('alt_text'),
            'description' => $request->get('description'),
        ]);

        // Extract and store metadata
        $this->extractMetadata($media, $file);

        // Generate thumbnails for images
        if ($media->isImage()) {
            try {
                $media->generateThumbnails();
                Log::info('Thumbnails generated for media', [
                    'media_id' => $media->id,
                    'filename' => $media->original_filename
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to generate thumbnails', [
                    'media_id' => $media->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $media;
    }

    /**
     * Get storage folder path for file upload.
     */
    private function getStorageFolderPath($request): string
    {
        $basePath = 'media';

        // If uploading to a specific folder
        $folderId = $request->get('folder_id');
        if ($folderId) {
            $folder = MediaFolder::find($folderId);
            if ($folder) {
                return $basePath . '/' . $folder->path;
            }
        }

        // Default to root media folder
        return $basePath;
    }

    /**
     * Move media file physically to new folder.
     */
    private function moveMediaFile(Media $media, $newFolderId): bool
    {
        try {
            $oldPath = $media->path;
            $filename = basename($oldPath);

            // Get new folder path
            $newFolderPath = 'media';
            if ($newFolderId) {
                $folder = MediaFolder::find($newFolderId);
                if ($folder) {
                    $newFolderPath = 'media/' . $folder->path;
                }
            }

            $newPath = $newFolderPath . '/' . $filename;

            // Move the main file
            if (Storage::disk('public')->exists($oldPath)) {
                // Ensure target directory exists
                $targetDir = dirname($newPath);
                if (!Storage::disk('public')->exists($targetDir)) {
                    Storage::disk('public')->makeDirectory($targetDir);
                }

                // Move the file
                Storage::disk('public')->move($oldPath, $newPath);

                // Update media path in database
                $media->path = $newPath;

                // Move thumbnails if they exist
                $this->moveThumbnails($media, $oldPath, $newPath);

                Log::info('Media file moved successfully', [
                    'media_id' => $media->id,
                    'old_path' => $oldPath,
                    'new_path' => $newPath
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Failed to move media file', [
                'media_id' => $media->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Move thumbnail files to new location.
     */
    private function moveThumbnails(Media $media, string $oldPath, string $newPath): void
    {
        if (!$media->isImage()) {
            return;
        }

        $thumbnailSizes = config('media.thumbnail_sizes', []);
        $oldBasename = pathinfo($oldPath, PATHINFO_FILENAME);
        $newBasename = pathinfo($newPath, PATHINFO_FILENAME);
        $extension = pathinfo($oldPath, PATHINFO_EXTENSION);

        foreach ($thumbnailSizes as $sizeName => $config) {
            $oldThumbnailPath = dirname($oldPath) . '/' . $oldBasename . '-' . $config['width'] . 'x' . $config['height'] . '.' . $extension;
            $newThumbnailPath = dirname($newPath) . '/' . $newBasename . '-' . $config['width'] . 'x' . $config['height'] . '.' . $extension;

            if (Storage::disk('public')->exists($oldThumbnailPath)) {
                Storage::disk('public')->move($oldThumbnailPath, $newThumbnailPath);
            }
        }
    }

    /**
     * Extract metadata from uploaded file.
     */
    private function extractMetadata(Media $media, $file): void
    {
        $metadata = [];

        if ($media->isImage()) {
            try {
                $imageInfo = getimagesize($file->getPathname());
                if ($imageInfo) {
                    $metadata['width'] = $imageInfo[0];
                    $metadata['height'] = $imageInfo[1];
                }

                // Extract EXIF data if available
                if (function_exists('exif_read_data') && in_array($media->extension, ['jpg', 'jpeg'])) {
                    $exif = @exif_read_data($file->getPathname());
                    if ($exif) {
                        $metadata['exif_data'] = $exif;

                        // Extract specific EXIF fields
                        if (isset($exif['Make'])) $metadata['camera_make'] = $exif['Make'];
                        if (isset($exif['Model'])) $metadata['camera_model'] = $exif['Model'];
                        if (isset($exif['FocalLength'])) $metadata['focal_length'] = $this->parseExifRational($exif['FocalLength']);
                        if (isset($exif['FNumber'])) $metadata['aperture'] = $this->parseExifRational($exif['FNumber']);
                        if (isset($exif['ISOSpeedRatings'])) $metadata['iso'] = $exif['ISOSpeedRatings'];
                        if (isset($exif['ExposureTime'])) $metadata['shutter_speed'] = $this->parseExifRational($exif['ExposureTime']);
                    }
                }
            } catch (\Exception $e) {
                // Ignore metadata extraction errors
            }
        }

        if (!empty($metadata)) {
            MediaMetadata::create(array_merge(['media_id' => $media->id], $metadata));
        }
    }

    /**
     * Parse EXIF rational values.
     */
    private function parseExifRational($rational): ?float
    {
        if (is_string($rational) && strpos($rational, '/') !== false) {
            [$numerator, $denominator] = explode('/', $rational);
            return $denominator != 0 ? $numerator / $denominator : null;
        }

        return is_numeric($rational) ? (float) $rational : null;
    }

    /**
     * Get file extensions by type.
     */
    private function getExtensionsByType(string $type): array
    {
        return match ($type) {
            'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'],
            'video' => ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv'],
            'audio' => ['mp3', 'wav', 'ogg', 'aac', 'flac'],
            'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
            default => [],
        };
    }
}
