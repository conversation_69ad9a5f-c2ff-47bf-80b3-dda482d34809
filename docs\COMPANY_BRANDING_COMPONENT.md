# Company Branding Component

## Overview

The `CompanyBranding` component replaces the `AppTeamManager` component in the sidebar header. It displays role-based company/business branding with custom logos, names, and brand colors.

## Features

### 🎨 **Role-Based Branding**
- **Platform Administrator**: Shows "MLS Platform" with blue branding
- **Brokerage Admin**: Shows company name with green branding  
- **Independent Agent**: Shows personal business name with purple branding
- **Company Agent**: Shows company name with agent designation

### 🖼️ **Logo Support**
- Custom logo images with fallback to default SVG
- Automatic error handling for broken image URLs
- Responsive design that works in collapsed sidebar

### 🎯 **Dynamic Content**
- Company names from team/user data
- Role-appropriate subtitles
- Custom brand colors per role type

## Component Structure

```jsx
<CompanyBranding />
```

### Props
The component automatically receives data from Inertia's shared props:
- `auth.user` - Current user information
- `companyBranding` - Branding settings (logo, colors, names)

## Implementation

### 1. Component Location
```
resources/js/Components/CompanyBranding.jsx
```

### 2. Usage in Layout
```jsx
// resources/js/Layouts/AppLayout.jsx
import CompanyBranding from '@/Components/CompanyBranding'

function AppSidebar() {
  return (
    <Sidebar collapsible="icon" className="border-r">
      <SidebarHeader>
        <CompanyBranding />  {/* Replaces AppTeamManager */}
      </SidebarHeader>
      {/* ... */}
    </Sidebar>
  )
}
```

### 3. Backend Integration
```php
// app/Http/Middleware/HandleInertiaRequests.php
private function getCompanyBranding(Request $request): ?array
{
    $user = $request->user();
    
    // Role-based branding logic
    switch ($user->user_type->value) {
        case 'platform_administrator':
            return ['company_name' => 'MLS Platform', 'brand_color' => '#3b82f6'];
        case 'brokerage_admin':
        case 'company_agent':
            return ['company_name' => $user->currentTeam->name, 'brand_color' => '#10b981'];
        case 'independent_agent':
            return ['business_name' => $user->name . ' Real Estate', 'brand_color' => '#8b5cf6'];
    }
}
```

## Branding Rules

### Platform Administrator
- **Name**: "MLS Platform"
- **Subtitle**: "Platform Administration"
- **Color**: Blue (#3b82f6)
- **Logo**: Default building SVG

### Brokerage Admin
- **Name**: Company/Team name
- **Subtitle**: "Brokerage Management"
- **Color**: Green (#10b981)
- **Logo**: Company logo or default SVG

### Independent Agent
- **Name**: "[Agent Name] Real Estate"
- **Subtitle**: "Independent Agent"
- **Color**: Purple (#8b5cf6)
- **Logo**: Personal logo or default SVG

### Company Agent
- **Name**: Company/Team name
- **Subtitle**: "Agent at [Company Name]"
- **Color**: Green (#10b981)
- **Logo**: Company logo or default SVG

## Default SVG Logo

The component includes a custom real estate-themed SVG logo:

```jsx
const CompanyLogo = ({ className = "size-8" }) => (
  <svg className={className} viewBox="0 0 100 100">
    {/* Building structure with windows and door */}
    <path d="M15 25 L50 10 L85 25 L85 85 L15 85 Z" fill="currentColor" />
    {/* Windows and door details */}
  </svg>
)
```

## Customization

### Adding Custom Logos
1. Add logo URL to company/user settings
2. Logo will automatically display with fallback to default SVG
3. Supports any web-compatible image format

### Custom Brand Colors
1. Set `brand_color` in the branding data
2. Accepts any valid CSS color value
3. Applied to the logo container background

### Custom Names
1. Set `company_name` or `business_name` in branding data
2. Automatically truncated for long names
3. Falls back to team/user name if not set

## Future Enhancements

### Database Integration
- Add `logo_url` field to teams table for company logos
- Add `business_logo_url` field to users table for independent agents
- Add `brand_color` customization in settings

### Settings Integration
- Connect to company settings for brokerage admins
- Connect to agent settings for independent agents
- Real-time updates when settings change

### Advanced Features
- Logo upload functionality
- Brand color picker
- Custom business name editor
- Logo positioning options

## Testing

Test the component with different user types:

1. **Platform Admin**: `<EMAIL>`
   - Should show "MLS Platform" with blue background

2. **Brokerage Admin**: `<EMAIL>`
   - Should show company name with green background

3. **Independent Agent**: `<EMAIL>`
   - Should show "[Name] Real Estate" with purple background

4. **Company Agent**: `<EMAIL>`
   - Should show company name with "Agent at [Company]" subtitle

## Benefits

✅ **Role-appropriate branding** for each user type  
✅ **Professional appearance** with custom logos and colors  
✅ **Consistent design** across all user roles  
✅ **Extensible architecture** for future customization  
✅ **Error handling** for broken images  
✅ **Responsive design** for collapsed sidebar states
