import { <PERSON>, <PERSON> } from '@inertiajs/react'
import { Bell, Database, Eye, Globe, Languages, Mail, Palette, Settings, Shield, Zap } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import AppLayout from '@/Layouts/AppLayout'

export default function SettingsIndex({ sections, pageTitle = 'Settings', pageDescription = 'Manage your settings and preferences.', routePrefix = 'admin.settings' }) {
  // Icon mapping for different sections
  const getIcon = (sectionKey) => {
    const iconMap = {
      general: Settings,
      appearance: Palette,
      notifications: Bell,
      security: Shield,
      email: Mail,
      media: Eye,
      database: Database,
      cache: Zap,
      languages: Languages,
      localization: Globe,
    }
    return iconMap[sectionKey] || Settings
  }

  // Color mapping for different sections
  const getColor = (sectionKey) => {
    const colorMap = {
      general: 'text-blue-600',
      appearance: 'text-purple-600',
      notifications: 'text-yellow-600',
      security: 'text-red-600',
      email: 'text-green-600',
      media: 'text-indigo-600',
      database: 'text-gray-600',
      cache: 'text-orange-600',
      languages: 'text-pink-600',
      localization: 'text-teal-600',
    }
    return colorMap[sectionKey] || 'text-blue-600'
  }

  return (
    <AppLayout
      title={pageTitle}
      renderHeader={() => (
        <div className="flex items-center space-x-4">
          <Settings className="h-8 w-8" />
          <div>
            <h2 className="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
              {pageTitle}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {pageDescription}
            </p>
          </div>
        </div>
      )}
    >
      <Head title={pageTitle} />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          {/* Settings Categories Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(sections).map(([sectionKey, section]) => {
              const IconComponent = getIcon(sectionKey)
              const iconColor = getColor(sectionKey)

              return (
                <Link
                  key={sectionKey}
                  href={route(`${routePrefix}.${sectionKey}`)}
                  className="block"
                >
                  <Card className="h-full cursor-pointer">
                    <CardHeader className="pb-3">
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg bg-gray-50 dark:bg-gray-800 ${iconColor}`}>
                          <IconComponent className="h-6 w-6" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {section.title}
                          </CardTitle>
                          <CardDescription className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {section.description}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {Object.keys(section.settings).length}
                        {' '}
                        setting
                        {Object.keys(section.settings).length !== 1 ? 's' : ''}
                        {' '}
                        available
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>

          {/* Quick Info */}
          <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <h3 className="font-medium text-blue-900 dark:text-blue-100">Settings Overview</h3>
            </div>
            <p className="mt-2 text-sm text-blue-700 dark:text-blue-300">
              Click on any category above to configure specific settings. Changes are saved automatically when you submit each form.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
