import { Icon } from '@iconify/react'
import { <PERSON> } from '@inertiajs/react'
import { route } from 'ziggy-js'
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/shadcn/ui/avatar'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import StatsCard from '@/Components/StatsCard'
import AppLayout from '@/Layouts/AppLayout'

const defaultRecentAgents = []

export default function CompanyAdminDashboard({ user, stats, recent_agents = defaultRecentAgents }) {
  const quickActions = [
    {
      title: 'Add New Agent',
      description: 'Add agents to your company',
      icon: 'lucide:user-plus',
      href: route('company.agents.create'),
      color: 'bg-blue-500',
    },
    {
      title: 'Manage Listings',
      description: 'View all company listings',
      icon: 'lucide:home',
      href: '#', // TODO: Add route when listings feature is implemented
      color: 'bg-green-500',
    },
    {
      title: 'Company Reports',
      description: 'View company performance',
      icon: 'lucide:bar-chart-3',
      href: '#', // TODO: Add route when reports feature is implemented
      color: 'bg-purple-500',
    },
    {
      title: 'Manage Agents',
      description: 'View and manage all agents',
      icon: 'lucide:users',
      href: route('company.agents.index'),
      color: 'bg-purple-500',
    },
    {
      title: 'Account Settings',
      description: 'Manage account profile',
      icon: 'lucide:settings',
      href: route('profile.show'), // Link to profile instead of team settings
      color: 'bg-orange-500',
    },
  ]

  const recentActivity = [
    {
      type: 'agent',
      title: 'New agent joined',
      description: 'Sarah Johnson joined the company',
      time: '2 hours ago',
      icon: 'lucide:user-plus',
    },
    {
      type: 'listing',
      title: 'New listing added',
      description: 'Mike added 789 Pine St',
      time: '4 hours ago',
      icon: 'lucide:home-plus',
    },
    {
      type: 'deal',
      title: 'Deal closed',
      description: 'Lisa closed $450K deal',
      time: '1 day ago',
      icon: 'lucide:handshake',
    },
  ]

  return (
    <AppLayout title="Company Dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {user.company_name || `${user.name}'s Company`}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your real estate company and track performance.
            </p>
          </div>
          <Badge variant="outline" className="text-sm">
            Brokerage Admin
          </Badge>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            value={stats.total_agents}
            description="Total Agents"
            icon="lucide:users"
          />
          <StatsCard
            value={stats.active_listings}
            description="Active Listings"
            icon="lucide:home"
          />
          <StatsCard
            value={stats.pending_leads}
            description="Pending Leads"
            icon="lucide:user-check"
          />
          <StatsCard
            value={`$${stats.monthly_revenue.toLocaleString()}`}
            description="Monthly Revenue"
            icon="lucide:dollar-sign"
          />
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon icon="lucide:zap" className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Manage your company and business operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map(action => (
                <Link
                  key={action.title}
                  href={action.href}
                  className="group block"
                >
                  <div className="rounded-lg border p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div className="flex items-center gap-3">
                      <div className={`rounded-lg p-2 ${action.color} text-white`}>
                        <Icon icon={action.icon} className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400">
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Company Overview and Recent Activity */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Company Agents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:users" className="h-5 w-5" />
                  Company Agents
                </div>
                <Link
                  href={route('company.agents.index')}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  View All
                </Link>
              </CardTitle>
              <CardDescription>
                Your recent agent additions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recent_agents.length > 0
                  ? (
                      recent_agents.map(agent => (
                        <div key={agent.id} className="flex items-center gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={agent.profile_photo_url} />
                            <AvatarFallback>
                              {agent.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{agent.name}</p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {agent.email}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            Agent
                          </Badge>
                        </div>
                      ))
                    )
                  : (
                      <div className="text-center py-6">
                        <Icon icon="lucide:users" className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                          No agents yet
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Start by adding agents to your company.
                        </p>
                        <div className="mt-6">
                          <Link href={route('company.agents.create')}>
                            <Button>
                              <Icon icon="lucide:user-plus" className="mr-2 h-4 w-4" />
                              Add First Agent
                            </Button>
                          </Link>
                        </div>
                      </div>
                    )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon icon="lucide:activity" className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Latest company activities and updates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="rounded-full bg-gray-100 p-2 dark:bg-gray-800">
                      <Icon icon={activity.icon} className="h-4 w-4" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Company Setup Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon icon="lucide:rocket" className="h-5 w-5" />
              Company Setup Progress
            </CardTitle>
            <CardDescription>
              Complete these steps to get your company up and running
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-green-100 p-1 dark:bg-green-900">
                  <Icon icon="lucide:check" className="h-3 w-3 text-green-600 dark:text-green-400" />
                </div>
                <span className="text-sm">Company registered</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-gray-100 p-1 dark:bg-gray-800">
                  <Icon icon="lucide:circle" className="h-3 w-3" />
                </div>
                <span className="text-sm">Add company agents</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-gray-100 p-1 dark:bg-gray-800">
                  <Icon icon="lucide:circle" className="h-3 w-3" />
                </div>
                <span className="text-sm">Set up listings</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="rounded-full bg-gray-100 p-1 dark:bg-gray-800">
                  <Icon icon="lucide:circle" className="h-3 w-3" />
                </div>
                <span className="text-sm">Configure permissions</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
