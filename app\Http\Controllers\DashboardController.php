<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\User;
use App\Enums\UserType;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Auth;


final class DashboardController extends Controller
{
    public function __invoke(): Response
    {
        /** @var User $user */
        $user = Auth::user();

        // Check if user is super admin
        if ($user->isPlatformAdministrator()) {
            return $this->superAdminDashboard($user);
        }

        // Route to appropriate dashboard based on user type
        return match ($user->user_type) {
            UserType::INDEPENDENT_AGENT => $this->individualAgentDashboard($user),
            UserType::BROKERAGE_ADMIN => $this->companyAdminDashboard($user),
            UserType::COMPANY_AGENT => $this->individualAgentDashboard($user), // Company agents use same dashboard as individual agents
            UserType::PLATFORM_ADMINISTRATOR => $this->superAdminDashboard($user),
            default => $this->defaultDashboard($user),
        };
    }

    /**
     * Dashboard for super administrators
     */
    private function superAdminDashboard(User $user): Response
    {
        return Inertia::render('Dashboard/SuperAdmin', [
            'user' => $user,
            'stats' => [
                'total_users' => User::count(),
                'total_teams' => \App\Models\Team::count(),
                'total_independent_agents' => User::where('user_type', UserType::INDEPENDENT_AGENT)->count(),
                'total_companies' => User::where('user_type', UserType::BROKERAGE_ADMIN)->count(),
            ],
            'recent_users' => User::latest()->take(5)->get(),
            'recent_teams' => \App\Models\Team::latest()->take(5)->get(),
        ]);
    }

    /**
     * Dashboard for individual agents
     */
    private function individualAgentDashboard(User $user): Response
    {
        return Inertia::render('Dashboard/IndividualAgent', [
            'user' => $user,
            'stats' => [
                'active_listings' => 0, // TODO: Implement when listings feature is added
                'pending_leads' => 0,   // TODO: Implement when leads feature is added
                'closed_deals' => 0,    // TODO: Implement when deals feature is added
                'total_commission' => 0, // TODO: Implement when commission tracking is added
            ],
        ]);
    }

    /**
     * Dashboard for company administrators
     */
    private function companyAdminDashboard(User $user): Response
    {
        // Get agents managed by this company admin
        $managedAgents = User::where('user_type', UserType::COMPANY_AGENT)
            ->whereHas('teams', function ($query) use ($user) {
                $query->whereIn('teams.id', $user->ownedTeams()->pluck('id'));
            })
            ->get();

        return Inertia::render('Dashboard/CompanyAdmin', [
            'user' => $user,
            'stats' => [
                'total_agents' => $managedAgents->count(),
                'active_listings' => 0, // TODO: Implement when listings feature is added
                'pending_leads' => 0,   // TODO: Implement when leads feature is added
                'monthly_revenue' => 0, // TODO: Implement when revenue tracking is added
            ],
            'recent_agents' => $managedAgents->take(5),
        ]);
    }

    /**
     * Default dashboard fallback
     */
    private function defaultDashboard(User $user): Response
    {
        return Inertia::render('Dashboard', [
            'user' => $user,
        ]);
    }
}
