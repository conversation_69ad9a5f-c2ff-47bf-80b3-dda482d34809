'use client'

import { Link, router } from '@inertiajs/react'
import { Edit, Eye, MoreHorizontal, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { route } from 'ziggy-js'
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/shadcn/ui/avatar'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Checkbox } from '@/Components/shadcn/ui/checkbox'
import { ConfirmDialog } from '@/Components/shadcn/ui/confirm-dialog'
import { DataTableColumnHeader } from '@/Components/shadcn/ui/data-table-column-header'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/Components/shadcn/ui/dropdown-menu'

export const columns = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected()
          || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Agent" />
    ),
    cell: ({ row }) => {
      const agent = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={agent.profile_photo_url} alt={agent.name} />
            <AvatarFallback>
              {agent.name?.charAt(0)?.toUpperCase() || 'A'}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{agent.name}</div>
            <div className="text-sm text-muted-foreground">{agent.email}</div>
            {agent.phone && (
              <div className="text-sm text-muted-foreground">{agent.phone}</div>
            )}
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'role',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Role" />
    ),
    cell: ({ row }) => {
      const agent = row.original
      const role = agent.pivot?.role || 'agent'

      const getVariant = (role) => {
        switch (role?.toLowerCase()) {
          case 'team_lead':
            return 'destructive'
          case 'senior_agent':
            return 'default'
          case 'agent':
            return 'secondary'
          default:
            return 'outline'
        }
      }

      const getLabel = (role) => {
        switch (role?.toLowerCase()) {
          case 'team_lead':
            return 'Team Lead'
          case 'senior_agent':
            return 'Senior Agent'
          case 'agent':
            return 'Agent'
          default:
            return role || 'Agent'
        }
      }

      return (
        <Badge variant={getVariant(role)}>
          {getLabel(role)}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      const agent = row.original
      const role = agent.pivot?.role || 'agent'
      return value.includes(role)
    },
  },
  {
    accessorKey: 'license_number',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="License" />
    ),
    cell: ({ row }) => {
      const agent = row.original
      return (
        <div>
          <div className="font-medium">
            {agent.license_number || 'Not provided'}
          </div>
          {agent.license_state && (
            <div className="text-sm text-muted-foreground">
              {agent.license_state}
            </div>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Joined" />
    ),
    cell: ({ row }) => {
      const agent = row.original
      const joinedDate = agent.pivot?.created_at || agent.created_at
      return (
        <div className="text-sm">
          {new Date(joinedDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const agent = row.original
      const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

      const handleDelete = () => {
        router.delete(route('company.agents.destroy', agent.id), {
          preserveScroll: true,
          onSuccess: () => {
            toast.success('Agent removed successfully')
          },
          onError: () => {
            toast.error('Failed to remove agent')
          },
        })
      }

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={route('company.agents.show', agent.id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={route('company.agents.edit', agent.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Agent
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setShowDeleteConfirm(true)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remove Agent
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <ConfirmDialog
            isOpen={showDeleteConfirm}
            onClose={() => setShowDeleteConfirm(false)}
            onConfirm={handleDelete}
            title="Remove Agent"
            description={`Are you sure you want to remove "${agent.name}" from the company? This action cannot be undone.`}
            confirmText="Remove"
            cancelText="Cancel"
            variant="destructive"
          />
        </>
      )
    },
  },
]
