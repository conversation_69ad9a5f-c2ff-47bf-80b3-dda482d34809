'use client'

import { Eye, EyeOff } from 'lucide-react'
import { Button } from '@/Components/shadcn/ui/button'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/Components/shadcn/ui/dropdown-menu'

export function DataTableViewOptions({ table }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="ml-auto">
          👁️ View Columns
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end">
        {table
          .getAllColumns()
          .filter(column => column.getCanHide())
          .map((column) => {
            // agar header string hai to wahi show karega
            // warna fallback ke liye column.id
            const label
              = typeof column.columnDef.header === 'string'
                ? column.columnDef.header
                : column.id

            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={column.getIsVisible()}
                onCheckedChange={value => column.toggleVisibility(!!value)}
              >
                {column.getIsVisible()
                  ? (
                      <Eye className="mr-2 h-4 w-4 text-muted-foreground" />
                    )
                  : (
                      <EyeOff className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                {label}
              </DropdownMenuCheckboxItem>
            )
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
