import { Head, <PERSON> } from '@inertiajs/react'
import axios from 'axios'
import { Plus } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Button } from '@/Components/shadcn/ui/button'
import { ShadcnDataTable } from '@/Components/shadcn/ui/shadcn-data-table'
import AppLayout from '@/Layouts/AppLayout'
import { columns } from './columns'

export default function AgentsIndex({ agents: initialAgents, filters }) {
  const [data, setData] = useState(initialAgents?.data || [])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setData(initialAgents?.data || [])
  }, [initialAgents])

  const fetchData = async (params = {}) => {
    setLoading(true)
    try {
      const response = await axios.get(route('company.agents.index'), {
        params: {
          ...params,
          format: 'json',
        },
      })
      setData(response.data.agents?.data || [])
    }
    catch (error) {
      console.error('Error fetching agents:', error)
    }
    finally {
      setLoading(false)
    }
  }

  return (
    <AppLayout title="Company Agents">
      <Head title="Company Agents" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Company Agents</h1>
            <p className="text-muted-foreground">
              Manage your company agents and their roles
            </p>
          </div>
        </div>

        {/* DataTable */}
        <ShadcnDataTable
          columns={columns}
          data={data}
          filterableColumns={[
            {
              id: 'role',
              title: 'Role',
              options: [
                { label: 'Agent', value: 'agent' },
                { label: 'Senior Agent', value: 'senior_agent' },
                { label: 'Team Lead', value: 'team_lead' },
              ],
            },
          ]}
          addButton={(
            <Link href={route('company.agents.create')}>
              <Button size="sm" className="h-8">
                <Plus className="h-4 w-4 mr-2" />
                Add Agent
              </Button>
            </Link>
          )}
        />
      </div>
    </AppLayout>
  )
}
