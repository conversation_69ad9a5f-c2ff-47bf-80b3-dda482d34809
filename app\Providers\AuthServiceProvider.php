<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\User;
use App\Policies\SettingsPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => SettingsPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define gates for settings access
        Gate::define('access-branding-settings', [SettingsPolicy::class, 'accessBrandingSettings']);
        Gate::define('access-company-settings', [SettingsPolicy::class, 'accessCompanySettings']);
        Gate::define('access-platform-settings', [SettingsPolicy::class, 'accessPlatformSettings']);
        Gate::define('access-personal-preferences', [SettingsPolicy::class, 'accessPersonalPreferences']);
    }
}
