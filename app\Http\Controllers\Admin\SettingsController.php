<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use Inertia\Inertia;
use Inertia\Response;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified', 'platform.admin']);
    }

    /**
     * Display the settings overview page.
     */
    public function index(): Response
    {
        $sections = $this->getSettingSections();

        return Inertia::render('Admin/Settings/Index', [
            'sections' => $sections,
            'pageTitle' => 'Platform Settings',
            'pageDescription' => 'Manage platform-wide settings and configuration.',
            'routePrefix' => 'admin.settings',
        ]);
    }

    /**
     * Display general settings.
     */
    public function general(): Response
    {
        return $this->renderSection('general');
    }

    /**
     * Display appearance settings.
     */
    public function appearance(): Response
    {
        return $this->renderSection('appearance');
    }

    /**
     * Display notification settings.
     */
    public function notifications(): Response
    {
        return $this->renderSection('notifications');
    }

    /**
     * Display security settings.
     */
    public function security(): Response
    {
        return $this->renderSection('security');
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'general');
    }

    /**
     * Update appearance settings.
     */
    public function updateAppearance(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'appearance');
    }

    /**
     * Update notification settings.
     */
    public function updateNotifications(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'notifications');
    }

    /**
     * Update security settings.
     */
    public function updateSecurity(Request $request): RedirectResponse
    {
        return $this->updateSection($request, 'security');
    }

    /**
     * Render a specific settings section.
     */
    private function renderSection(string $sectionKey): Response
    {
        $sections = $this->getSettingSections();

        if (!isset($sections[$sectionKey])) {
            abort(404);
        }

        $section = $sections[$sectionKey];
        $allSettings = app('settings')->all();

        // Prepare settings data for the section
        $settingsData = [];
        foreach ($section['settings'] as $settingKey => $settingConfig) {
            $settingsData[$settingKey] = [
                'label' => $settingConfig['label'],
                'description' => $settingConfig['description'] ?? '',
                'type' => $settingConfig['type'],
                'value' => $allSettings[$settingKey] ?? $settingConfig['default'] ?? null,
                'options' => $settingConfig['options'] ?? null,
            ];
        }

        return Inertia::render('Admin/Settings/Section', [
            'sectionKey' => $sectionKey,
            'section' => [
                'title' => $section['title'],
                'description' => $section['description'],
                'settings' => $settingsData,
            ],
            'allSections' => $sections,
            'routePrefix' => 'admin.settings',
        ]);
    }

    /**
     * Update settings for a specific section.
     */
    private function updateSection(Request $request, string $sectionKey): RedirectResponse
    {
        $sections = $this->getSettingSections();

        if (!isset($sections[$sectionKey])) {
            abort(404);
        }

        $section = $sections[$sectionKey];
        $rules = [];

        // Build validation rules for this section only
        foreach ($section['settings'] as $key => $config) {
            $rules[$key] = $config['validation'] ?? 'nullable';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();
            app('settings')->bulkUpdate($data);

            return back()->with('success', 'Settings updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()]);
        }
    }

    /**
     * Get setting sections configuration.
     */
    private function getSettingSections(): array
    {
        return [
            'general' => [
                'title' => 'General Settings',
                'description' => 'Basic application settings',
                'settings' => [
                    'app_name' => [
                        'label' => 'Application Name',
                        'description' => 'The name of your application',
                        'type' => 'text',
                        'default' => 'MLS Platform',
                        'validation' => 'required|string|max:255',
                    ],
                    'app_description' => [
                        'label' => 'Application Description',
                        'description' => 'Brief description of your application',
                        'type' => 'textarea',
                        'default' => 'Professional MLS Platform',
                        'validation' => 'nullable|string|max:500',
                    ],
                    'timezone' => [
                        'label' => 'Default Timezone',
                        'description' => 'Default timezone for the application',
                        'type' => 'select',
                        'default' => 'UTC',
                        'options' => [
                            'UTC' => 'UTC',
                            'America/New_York' => 'Eastern Time',
                            'America/Chicago' => 'Central Time',
                            'America/Denver' => 'Mountain Time',
                            'America/Los_Angeles' => 'Pacific Time',
                        ],
                        'validation' => 'required|string',
                    ],
                    'date_format' => [
                        'label' => 'Date Format',
                        'description' => 'Default date format for the application',
                        'type' => 'select',
                        'default' => 'Y-m-d',
                        'options' => [
                            'Y-m-d' => '2024-01-15',
                            'm/d/Y' => '01/15/2024',
                            'd/m/Y' => '15/01/2024',
                            'F j, Y' => 'January 15, 2024',
                        ],
                        'validation' => 'required|string',
                    ],
                ]
            ],
            'appearance' => [
                'title' => 'Appearance',
                'description' => 'Customize the look and feel',
                'settings' => [
                    'theme_mode' => [
                        'label' => 'Default Theme',
                        'description' => 'Default theme for new users',
                        'type' => 'select',
                        'default' => 'light',
                        'options' => [
                            'light' => 'Light',
                            'dark' => 'Dark',
                            'system' => 'System',
                        ],
                        'validation' => 'required|in:light,dark,system',
                    ],
                    'primary_color' => [
                        'label' => 'Primary Color',
                        'description' => 'Primary brand color',
                        'type' => 'color',
                        'default' => '#3b82f6',
                        'validation' => 'required|string',
                    ],
                    'logo_url' => [
                        'label' => 'Logo URL',
                        'description' => 'URL to your application logo',
                        'type' => 'url',
                        'default' => '',
                        'validation' => 'nullable|url',
                    ],
                ]
            ],
            'notifications' => [
                'title' => 'Notifications',
                'description' => 'Configure notification settings',
                'settings' => [
                    'email_notifications' => [
                        'label' => 'Email Notifications',
                        'description' => 'Enable email notifications',
                        'type' => 'boolean',
                        'default' => true,
                        'validation' => 'boolean',
                    ],
                    'notification_email' => [
                        'label' => 'Notification Email',
                        'description' => 'Email address for system notifications',
                        'type' => 'email',
                        'default' => '',
                        'validation' => 'nullable|email',
                    ],
                ]
            ],
            'security' => [
                'title' => 'Security',
                'description' => 'Security and privacy settings',
                'settings' => [
                    'session_timeout' => [
                        'label' => 'Session Timeout (minutes)',
                        'description' => 'How long users stay logged in',
                        'type' => 'number',
                        'default' => 120,
                        'validation' => 'required|integer|min:5|max:1440',
                    ],
                    'password_min_length' => [
                        'label' => 'Minimum Password Length',
                        'description' => 'Minimum required password length',
                        'type' => 'number',
                        'default' => 8,
                        'validation' => 'required|integer|min:6|max:50',
                    ],
                    'require_email_verification' => [
                        'label' => 'Require Email Verification',
                        'description' => 'New users must verify their email',
                        'type' => 'boolean',
                        'default' => true,
                        'validation' => 'boolean',
                    ],
                ]
            ],
        ];
    }
}
