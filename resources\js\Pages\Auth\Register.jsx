import { Link, useForm, usePage } from '@inertiajs/react'
import { memo } from 'react'
import { route } from 'ziggy-js'
import InputError from '@/Components/InputError'
import AuthenticationCardLogo from '@/Components/LogoRedirect'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { Checkbox } from '@/Components/shadcn/ui/checkbox'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/shadcn/ui/select'
import { Textarea } from '@/Components/shadcn/ui/textarea'
import { useSeoMetaTags } from '@/Composables/useSeoMetaTags'

// User type options - Updated for new role hierarchy
const userTypeOptions = [
  { value: 'independent_agent', label: 'Independent Agent', description: 'Independent real estate agent working solo' },
  { value: 'brokerage_admin', label: 'Brokerage/Agency', description: 'Brokerage/Agency that can add and manage multiple agents' },
]

const teamTypeOptions = [
  { value: 'agency', label: 'Real Estate Agency' },
  { value: 'brokerage', label: 'Real Estate Brokerage' },
  { value: 'team', label: 'Real Estate Team' },
]

export default memo(() => {
  const { props: { jetstream } } = usePage()

  useSeoMetaTags({
    title: 'Register',
  })

  const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    user_type: '',
    phone: '',
    license_number: '',
    license_state: '',
    company_name: '',
    company_address: '',
    company_website: '',
    team_type: '',
    terms: false,
  })

  const submit = (e) => {
    e.preventDefault()
    form.post(route('register'), {
      onFinish: () => form.reset('password', 'password_confirmation'),
    })
  }

  const handleUserTypeChange = (value) => {
    form.setData('user_type', value)

    // Reset company fields if not brokerage admin
    if (value !== 'brokerage_admin') {
      form.setData({
        ...form.data,
        user_type: value,
        company_name: '',
        company_address: '',
        company_website: '',
        team_type: '',
      })
    }
  }

  const isBrokerageAdmin = form.data.user_type === 'brokerage_admin'

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <Card className="mx-auto max-w-lg">
        <CardHeader>
          <CardTitle className="flex justify-center">
            <AuthenticationCardLogo />
          </CardTitle>
          <CardDescription className="text-center text-2xl">
            Create your account
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={submit}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  type="text"
                  value={form.data.name}
                  onChange={e => form.setData('name', e.target.value)}
                  required
                  autoFocus
                  autoComplete="name"
                />
                <InputError message={form.errors.name} />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={form.data.email}
                  onChange={e => form.setData('email', e.target.value)}
                  required
                  autoComplete="username"
                />
                <InputError message={form.errors.email} />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="user_type">Account Type</Label>
                <Select value={form.data.user_type} onValueChange={handleUserTypeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your account type" />
                  </SelectTrigger>
                  <SelectContent>
                    {userTypeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex flex-col text-start">
                          <span className="font-medium">{option.label}</span>
                          <span className="text-xs text-muted-foreground">{option.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InputError message={form.errors.user_type} />
              </div>

              {/* Optional Profile Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="phone">Phone (Optional)</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={form.data.phone}
                    onChange={e => form.setData('phone', e.target.value)}
                    autoComplete="tel"
                  />
                  <InputError message={form.errors.phone} />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="license_number">License Number (Optional)</Label>
                  <Input
                    id="license_number"
                    type="text"
                    value={form.data.license_number}
                    onChange={e => form.setData('license_number', e.target.value)}
                  />
                  <InputError message={form.errors.license_number} />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="license_state">License State (Optional)</Label>
                <Input
                  id="license_state"
                  type="text"
                  value={form.data.license_state}
                  onChange={e => form.setData('license_state', e.target.value)}
                  placeholder="e.g., CA, NY, TX"
                  maxLength={2}
                />
                <InputError message={form.errors.license_state} />
              </div>

              {/* Company Fields for Brokerage Admins */}
              {isBrokerageAdmin && (
                <>
                  <div className="grid gap-2">
                    <Label htmlFor="company_name">Company Name</Label>
                    <Input
                      id="company_name"
                      type="text"
                      value={form.data.company_name}
                      onChange={e => form.setData('company_name', e.target.value)}
                      required={isBrokerageAdmin}
                    />
                    <InputError message={form.errors.company_name} />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="team_type">Company Type</Label>
                    <Select value={form.data.team_type} onValueChange={value => form.setData('team_type', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select company type" />
                      </SelectTrigger>
                      <SelectContent>
                        {teamTypeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <InputError message={form.errors.team_type} />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="company_address">Company Address (Optional)</Label>
                    <Textarea
                      id="company_address"
                      value={form.data.company_address}
                      onChange={e => form.setData('company_address', e.target.value)}
                      rows={3}
                    />
                    <InputError message={form.errors.company_address} />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="company_website">Company Website (Optional)</Label>
                    <Input
                      id="company_website"
                      type="url"
                      value={form.data.company_website}
                      onChange={e => form.setData('company_website', e.target.value)}
                      placeholder="https://example.com"
                    />
                    <InputError message={form.errors.company_website} />
                  </div>
                </>
              )}

              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={form.data.password}
                  onChange={e => form.setData('password', e.target.value)}
                  required
                  autoComplete="new-password"
                />
                <InputError message={form.errors.password} />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="password_confirmation">Confirm Password</Label>
                <Input
                  id="password_confirmation"
                  type="password"
                  value={form.data.password_confirmation}
                  onChange={e => form.setData('password_confirmation', e.target.value)}
                  required
                  autoComplete="new-password"
                />
                <InputError message={form.errors.password_confirmation} />
              </div>

              {jetstream?.hasTermsAndPrivacyPolicyFeature && (
                <div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="terms"
                      checked={form.data.terms}
                      onCheckedChange={checked => form.setData('terms', checked)}
                      name="terms"
                      required
                    />
                    <label htmlFor="terms" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      I agree to the
                      {' '}
                      <a target="_blank" href={route('terms.show')} className="rounded-md text-sm underline">
                        Terms of Service
                      </a>
                      {' '}
                      and
                      {' '}
                      <a target="_blank" href={route('policy.show')} className="rounded-md text-sm underline">
                        Privacy Policy
                      </a>
                    </label>
                  </div>
                  <InputError message={form.errors.terms} />
                </div>
              )}

              <div className="flex items-center justify-end gap-4">
                <Link href={route('login')} className="text-sm underline">
                  Already registered?
                </Link>

                <Button
                  className={form.processing ? 'opacity-25' : ''}
                  disabled={form.processing}
                >
                  Register
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
})
