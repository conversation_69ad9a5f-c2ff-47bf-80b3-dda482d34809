import { Icon } from '@iconify/react'
import { Head, <PERSON> } from '@inertiajs/react'
import { route } from 'ziggy-js'
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/shadcn/ui/avatar'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import AppLayout from '@/Layouts/AppLayout'

export default function ShowAgent({ agent }) {
  const getRoleBadge = (role) => {
    const variants = {
      agent: 'default',
      senior_agent: 'secondary',
      team_lead: 'destructive',
    }

    const labels = {
      agent: 'Agent',
      senior_agent: 'Senior Agent',
      team_lead: 'Team Lead',
    }

    return (
      <Badge variant={variants[role] || 'outline'}>
        {labels[role] || role}
      </Badge>
    )
  }

  const formatDate = (date) => {
    if (!date)
      return 'Not set'
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <AppLayout
      title="Agent Details"
      renderHeader={() => (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Agent Details
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              View
              {' '}
              {agent.name}
              's information and company details
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Link href={route('company.agents.edit', agent.id)}>
              <Button>
                <Icon icon="lucide:edit" className="mr-2 h-4 w-4" />
                Edit Agent
              </Button>
            </Link>
            <Link href={route('company.agents.index')}>
              <Button variant="outline">
                <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
                Back to Agents
              </Button>
            </Link>
          </div>
        </div>
      )}
    >
      <Head title={`Agent: ${agent.name}`} />

      <div className="py-12">
        <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
          <div className="space-y-6">
            {/* Agent Profile */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={agent.profile_photo_url} />
                      <AvatarFallback className="text-lg">
                        {agent.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h2 className="text-2xl font-bold">{agent.name}</h2>
                      <p className="text-gray-600 dark:text-gray-400">{agent.email}</p>
                    </div>
                  </div>
                  {getRoleBadge(agent.team_role)}
                </CardTitle>
                <CardDescription>Agent profile and contact information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Email Address</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{agent.email}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Phone Number</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{agent.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">User Type</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">Individual Agent</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Email Verified</h4>
                    <p className="mt-1">
                      {agent.email_verified_at
                        ? (
                            <Badge variant="default">Verified</Badge>
                          )
                        : (
                            <Badge variant="destructive">Not Verified</Badge>
                          )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* License Information */}
            <Card>
              <CardHeader>
                <CardTitle>License Information</CardTitle>
                <CardDescription>Real estate license details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">License Number</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{agent.license_number || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">License State</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{agent.license_state || 'Not provided'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">License Expiry</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">
                      {agent.license_expiry ? formatDate(agent.license_expiry) : 'Not provided'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
                <CardDescription>Agent's company assignment and role</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg border p-4 bg-gray-50 dark:bg-gray-800">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="rounded-lg bg-blue-500 p-2 text-white">
                          <Icon icon="lucide:building" className="h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {agent.team_name || 'Your Company'}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Company assignment
                          </p>
                        </div>
                      </div>
                      {getRoleBadge(agent.team_role)}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Company Role</h4>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">
                        {agent.team_role === 'agent'
                          ? 'Agent'
                          : agent.team_role === 'senior_agent'
                            ? 'Senior Agent'
                            : agent.team_role === 'team_lead' ? 'Team Lead' : agent.team_role}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Joined Company</h4>
                      <p className="mt-1 text-gray-600 dark:text-gray-400">
                        {formatDate(agent.team_joined_at)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Information */}
            <Card>
              <CardHeader>
                <CardTitle>Account Information</CardTitle>
                <CardDescription>Account creation and status details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Account Created</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{formatDate(agent.created_at)}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Last Updated</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">{formatDate(agent.updated_at)}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Account ID</h4>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">
                      #
                      {agent.id}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Account Status</h4>
                    <p className="mt-1">
                      <Badge variant="default">Active</Badge>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Manage this agent</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  <Link href={route('company.agents.edit', agent.id)}>
                    <Button>
                      <Icon icon="lucide:edit" className="mr-2 h-4 w-4" />
                      Edit Agent
                    </Button>
                  </Link>
                  <Button variant="outline" disabled>
                    <Icon icon="lucide:mail" className="mr-2 h-4 w-4" />
                    Send Message
                  </Button>
                  <Button variant="outline" disabled>
                    <Icon icon="lucide:file-text" className="mr-2 h-4 w-4" />
                    View Reports
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
