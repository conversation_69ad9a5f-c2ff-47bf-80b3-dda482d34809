import { Icon } from '@iconify/react'
import { Head, Link, useForm } from '@inertiajs/react'
import { route } from 'ziggy-js'
import InputError from '@/Components/InputError'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/shadcn/ui/select'
import AppLayout from '@/Layouts/AppLayout'

export default function EditAgent({ agent, availableRoles, currentRole }) {
  const { data, setData, put, processing, errors } = useForm({
    name: agent.name || '',
    email: agent.email || '',
    phone: agent.phone || '',
    license_number: agent.license_number || '',
    license_state: agent.license_state || '',
    license_expiry: agent.license_expiry || '',
    role: currentRole || 'agent',
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    put(route('company.agents.update', agent.id))
  }

  return (
    <AppLayout title="Edit Agent">
      <Head title={`Edit Agent: ${agent.name}`} />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Edit Agent</h1>
            <p className="text-muted-foreground">
              Update
              {' '}
              {agent.name}
              's information in your company
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href={route('company.agents.index')}>
              <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
              Back to Agents
            </Link>
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Update the agent's basic details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={data.name}
                      onChange={e => setData('name', e.target.value)}
                      required
                      placeholder="John Doe"
                    />
                    <InputError message={errors.name} />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={data.email}
                      onChange={e => setData('email', e.target.value)}
                      required
                      placeholder="<EMAIL>"
                    />
                    <InputError message={errors.email} />
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={data.phone}
                      onChange={e => setData('phone', e.target.value)}
                      placeholder="(*************"
                    />
                    <InputError message={errors.phone} />
                  </div>
                  <div>
                    <Label htmlFor="role">Company Role *</Label>
                    <Select value={data.role} onValueChange={value => setData('role', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableRoles.map(role => (
                          <SelectItem key={role.value} value={role.value}>
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <InputError message={errors.role} />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle>Company Assignment</CardTitle>
                <CardDescription>Agent's current company assignment</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-lg border p-4 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-blue-500 p-2 text-white">
                      <Icon icon="lucide:building" className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        Your Company
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Agent is assigned to your company with the selected role
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* License Information - Full Width */}
          <Card>
            <CardHeader>
              <CardTitle>License Information</CardTitle>
              <CardDescription>Real estate license details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <Label htmlFor="license_number">License Number</Label>
                  <Input
                    id="license_number"
                    type="text"
                    value={data.license_number}
                    onChange={e => setData('license_number', e.target.value)}
                    placeholder="RE123456"
                  />
                  <InputError message={errors.license_number} />
                </div>
                <div>
                  <Label htmlFor="license_state">License State</Label>
                  <Input
                    id="license_state"
                    type="text"
                    value={data.license_state}
                    onChange={e => setData('license_state', e.target.value)}
                    placeholder="CA"
                    maxLength={2}
                  />
                  <InputError message={errors.license_state} />
                </div>
                <div>
                  <Label htmlFor="license_expiry">License Expiry</Label>
                  <Input
                    id="license_expiry"
                    type="date"
                    value={data.license_expiry}
                    onChange={e => setData('license_expiry', e.target.value)}
                  />
                  <InputError message={errors.license_expiry} />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions - Full Width */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-end space-x-4">
                <Link href={route('company.agents.index')}>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
                <Button type="submit" disabled={processing}>
                  {processing
                    ? (
                        <>
                          <Icon icon="lucide:loader-2" className="mr-2 h-4 w-4 animate-spin" />
                          Updating Agent...
                        </>
                      )
                    : (
                        <>
                          <Icon icon="lucide:save" className="mr-2 h-4 w-4" />
                          Update Agent
                        </>
                      )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </AppLayout>
  )
}
