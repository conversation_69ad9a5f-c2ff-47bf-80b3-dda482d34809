import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react'
import { AlertCircle, ArrowLeft, CheckCircle, Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/Components/shadcn/ui/alert'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/shadcn/ui/select'
import { Switch } from '@/Components/shadcn/ui/switch'
import { Textarea } from '@/Components/shadcn/ui/textarea'
import AppLayout from '@/Layouts/AppLayout'

export default function SettingsSection({ sectionKey, section, allSections, routePrefix = 'admin.settings' }) {
  // Flatten settings for form data
  const initialData = {}
  Object.entries(section.settings).forEach(([key, setting]) => {
    initialData[key] = setting.value
  })

  const { data, setData, post, processing, errors, recentlySuccessful } = useForm(initialData)

  const handleSubmit = (e) => {
    e.preventDefault()
    post(route(`${routePrefix}.${sectionKey}.update`), {
      preserveScroll: true,
    })
  }

  const renderField = (key, setting) => {
    const value = data[key]
    const error = errors[key]

    switch (setting.type) {
      case 'text':
      case 'email':
      case 'url':
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{setting.label}</Label>
            <Input
              id={key}
              type={setting.type}
              value={value || ''}
              onChange={e => setData(key, e.target.value)}
              className={error ? 'border-red-500' : ''}
            />
            {setting.description && (
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            )}
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'textarea':
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{setting.label}</Label>
            <Textarea
              id={key}
              value={value || ''}
              onChange={e => setData(key, e.target.value)}
              className={error ? 'border-red-500' : ''}
              rows={3}
            />
            {setting.description && (
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            )}
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'select':
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{setting.label}</Label>
            <Select value={value || ''} onValueChange={newValue => setData(key, newValue)}>
              <SelectTrigger className={error ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                {setting.options && Object.entries(setting.options).map(([optionValue, optionLabel]) => (
                  <SelectItem key={optionValue} value={optionValue}>
                    {optionLabel}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {setting.description && (
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            )}
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'boolean':
        return (
          <div key={key} className="space-y-2">
            <div className="flex items-center space-x-2">
              <Switch
                id={key}
                checked={Boolean(value)}
                onCheckedChange={checked => setData(key, checked)}
              />
              <Label htmlFor={key}>{setting.label}</Label>
            </div>
            {setting.description && (
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            )}
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'number':
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{setting.label}</Label>
            <Input
              id={key}
              type="number"
              value={value || ''}
              onChange={e => setData(key, Number.parseInt(e.target.value) || '')}
              className={error ? 'border-red-500' : ''}
            />
            {setting.description && (
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            )}
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'color':
        return (
          <div key={key} className="space-y-2">
            <Label htmlFor={key}>{setting.label}</Label>
            <div className="flex items-center space-x-2">
              <Input
                id={key}
                type="color"
                value={value || '#000000'}
                onChange={e => setData(key, e.target.value)}
                className={`w-16 h-10 p-1 ${error ? 'border-red-500' : ''}`}
              />
              <Input
                type="text"
                value={value || ''}
                onChange={e => setData(key, e.target.value)}
                className={`flex-1 ${error ? 'border-red-500' : ''}`}
                placeholder="#000000"
              />
            </div>
            {setting.description && (
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            )}
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <AppLayout
      title={`${section.title} Settings`}
      renderHeader={() => (
        <div className="flex items-center space-x-4">
          <Link
            href={route(`${routePrefix}.index`)}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <ArrowLeft className="h-6 w-6" />
          </Link>
          <div>
            <h2 className="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
              {section.title}
              {' '}
              Settings
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {section.description}
            </p>
          </div>
        </div>
      )}
    >
      <Head title={`${section.title} Settings`} />

      <div className="py-12">
        <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
          {recentlySuccessful && (
            <Alert className="mb-6 border-green-200 bg-green-50 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Settings updated successfully.
              </AlertDescription>
            </Alert>
          )}

          {Object.keys(errors).length > 0 && (
            <Alert className="mb-6 border-red-200 bg-red-50 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please fix the errors below and try again.
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <Card>
              <CardHeader>
                <CardTitle>{section.title}</CardTitle>
                <CardDescription>{section.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {Object.entries(section.settings).map(([settingKey, setting]) =>
                  renderField(settingKey, setting),
                )}
              </CardContent>
            </Card>

            <div className="mt-6 flex justify-between">
              <Link
                href={route(`${routePrefix}.index`)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Settings
              </Link>
              <Button type="submit" disabled={processing}>
                {processing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {processing ? 'Saving...' : 'Save Settings'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  )
}
