<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\MediaController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\MediaFolderController;

/*
|--------------------------------------------------------------------------
| Platform Administrator Routes
|--------------------------------------------------------------------------
|
| These routes are specifically for Platform Administrators (Super Admin).
| They have the highest level of access and can manage the entire platform.
|
*/

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified', 'role:platform_administrator'])
    ->prefix('admin')
    ->name('admin.')
    ->group(function () {

        // ===================================================================
        // USER MANAGEMENT
        // ===================================================================
        Route::resource('users', UserController::class);
        Route::get('users/api/data', [UserController::class, 'apiIndex'])->name('users.api');

        // ===================================================================
        // PLAN MANAGEMENT
        // ===================================================================
        Route::resource('plans', PlanController::class);
        Route::get('plans/api/data', [PlanController::class, 'apiIndex'])->name('plans.api');

        // ===================================================================
        // MEDIA MANAGEMENT (Platform Level)
        // ===================================================================
        Route::resource('media', MediaController::class);
        Route::resource('media-folders', MediaFolderController::class);
        Route::post('media/bulk-destroy', [MediaController::class, 'bulkDestroy'])->name('media.bulk-destroy');
        Route::post('media/move-to-folder', [MediaController::class, 'moveToFolder'])->name('media.move-to-folder');

        // ===================================================================
        // PLATFORM SETTINGS (Super Admin only)
        // ===================================================================
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingsController::class, 'index'])->name('index');
            Route::get('/general', [SettingsController::class, 'general'])->name('general');
            Route::post('/general', [SettingsController::class, 'updateGeneral'])->name('general.update');
            Route::get('/appearance', [SettingsController::class, 'appearance'])->name('appearance');
            Route::post('/appearance', [SettingsController::class, 'updateAppearance'])->name('appearance.update');
            Route::get('/notifications', [SettingsController::class, 'notifications'])->name('notifications');
            Route::post('/notifications', [SettingsController::class, 'updateNotifications'])->name('notifications.update');
            Route::get('/security', [SettingsController::class, 'security'])->name('security');
            Route::post('/security', [SettingsController::class, 'updateSecurity'])->name('security.update');

            // Legacy API routes for backward compatibility
            Route::post('/', [SettingsController::class, 'store'])->name('store');
            Route::put('/{key}', [SettingsController::class, 'update'])->name('update');
            Route::delete('/{key}', [SettingsController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-update', [SettingsController::class, 'bulkUpdate'])->name('bulk-update');
            Route::get('/api/data', [SettingsController::class, 'apiIndex'])->name('api');
        });
    });
