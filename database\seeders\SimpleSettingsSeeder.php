<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SimpleSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $defaultSettings = [
            // General Settings
            'app_name' => 'MLS Platform',
            'app_description' => 'Professional MLS Platform',
            'timezone' => 'UTC',
            'date_format' => 'Y-m-d',

            // Appearance Settings
            'theme_mode' => 'light',
            'primary_color' => '#3b82f6',
            'logo_url' => '',

            // Notification Settings
            'email_notifications' => true,
            'notification_email' => '',

            // Security Settings
            'session_timeout' => 120,
            'password_min_length' => 8,
            'require_email_verification' => true,
        ];

        foreach ($defaultSettings as $key => $value) {
            Setting::updateOrCreate(
                [
                    'key' => $key,
                    'scope_type' => 'platform',
                    'scope_id' => null,
                ],
                ['value' => is_bool($value) ? json_encode($value) : $value]
            );
        }
    }
}
