import { router } from '@inertiajs/react'
import axios from 'axios'
import {
  ChevronLeft,
  ChevronRight,
  Copy,
  Edit3,
  File,
  FileText,
  Folder,
  FolderOpen,
  Image,
  Loader2,
  Music,
  Plus,
  Trash2,
  Upload,
  Video,
} from 'lucide-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { route } from 'ziggy-js'
import { useToast } from '@/Components/hooks/use-toast'
import { cn } from '@/Components/lib/utils'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent } from '@/Components/shadcn/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/Components/shadcn/ui/dialog'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { ScrollArea } from '@/Components/shadcn/ui/scroll-area'
import { Textarea } from '@/Components/shadcn/ui/textarea'

export default function EnhancedMediaLibrary({
  folders = [],
  media = [],
  currentFolder = null,
  onMediaClick,
  onMediaEdit,
  onMediaMove,
  onUploadComplete,
  userPermissions = {},
  uploadRoute,
}) {
  const { toast } = useToast()
  const fileInputRef = useRef(null)

  // State management
  const [selectedItem, setSelectedItem] = useState(null)
  const [sidebarVisible, setSidebarVisible] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [allMediaData, setAllMediaData] = useState(media.data || [])
  const [currentPage, setCurrentPage] = useState(media.current_page || 1)
  const [lastPage, setLastPage] = useState(media.last_page || 1)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Edit form state
  const [editData, setEditData] = useState({
    title: '',
    alt_text: '',
    description: '',
  })

  // Sync media data when props change
  useEffect(() => {
    // If we were refreshing and now have new data, stop refreshing
    if (isRefreshing) {
      setIsRefreshing(false)
    }

    setAllMediaData(media.data || [])
    setCurrentPage(media.current_page || 1)
    setLastPage(media.last_page || 1)
  }, [media, isRefreshing])

  // Listen for refresh events
  useEffect(() => {
    const handleRefreshStart = () => {
      setIsRefreshing(true)
    }

    window.addEventListener('mediaRefreshStart', handleRefreshStart)

    return () => {
      window.removeEventListener('mediaRefreshStart', handleRefreshStart)
    }
  }, [])

  // Listen for sidebar toggle events
  useEffect(() => {
    const handleToggleSidebar = () => {
      setSidebarVisible(prev => !prev)
    }

    window.addEventListener('toggleMediaSidebar', handleToggleSidebar)

    return () => {
      window.removeEventListener('toggleMediaSidebar', handleToggleSidebar)
    }
  }, [])

  const handleRefresh = useCallback(() => {
    window.dispatchEvent(new CustomEvent('mediaRefreshStart'))
    router.reload({ only: ['media'] })
  }, [])

  // Handle file selection
  const handleItemClick = useCallback((item, type = 'file') => {
    const itemWithType = { ...item, type }
    setSelectedItem(itemWithType)
    setSidebarVisible(true)
    setIsEditing(false)

    if (type === 'file') {
      setEditData({
        title: item.title || '',
        alt_text: item.alt_text || '',
        description: item.description || '',
      })
    }

    if (onMediaClick) {
      onMediaClick(item)
    }
  }, [onMediaClick])

  // Handle drag and drop
  const onDrop = useCallback(async (acceptedFiles) => {
    if (!userPermissions.canUpload) {
      toast({
        title: 'Permission Denied',
        description: 'You don\'t have permission to upload files.',
        variant: 'destructive',
      })
      return
    }

    setUploading(true)
    setUploadProgress(0)

    try {
      const formData = new FormData()
      acceptedFiles.forEach((file) => {
        formData.append('files[]', file)
      })

      if (currentFolder?.id) {
        formData.append('folder_id', currentFolder.id)
      }

      const response = await axios.post(uploadRoute, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total,
          )
          setUploadProgress(progress)
        },
      })

      if (response.data.success) {
        toast({
          title: 'Upload Successful',
          description: `${acceptedFiles.length} file(s) uploaded successfully.`,
        })

        if (onUploadComplete) {
          onUploadComplete()
        }
      }
    }
    catch (error) {
      console.error('Upload failed:', error)

      let errorMessage = 'Failed to upload files. Please try again.'

      if (error.response?.status === 422) {
        // Validation errors
        const errors = error.response.data.errors
        if (errors) {
          const errorMessages = Object.values(errors).flat()
          errorMessage = errorMessages.join(', ')
        }
      }
      else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      }

      toast({
        title: 'Upload Failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
    finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }, [currentFolder, uploadRoute, userPermissions.canUpload, onUploadComplete, toast])

  // Supported file extensions (Botble style - simplified)
  const supportedExtensions = [
    // Images
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    '.svg',
    '.bmp',
    // Videos
    '.mp4',
    '.webm',
    '.ogg',
    '.mov',
    '.avi',
    '.wmv',
    '.flv',
    '.mkv',
    // Audio
    '.mp3',
    '.wav',
    '.ogg',
    '.aac',
    '.flac',
    '.m4a',
    '.wma',
    // Documents
    '.pdf',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.ppt',
    '.pptx',
    '.txt',
    '.rtf',
    '.csv',
    // Archives
    '.zip',
    '.rar',
    '.7z',
    '.tar',
    '.gz',
  ]

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp'],
      'video/*': ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv', '.mkv'],
      'audio/*': ['.mp3', '.wav', '.ogg', '.aac', '.flac', '.m4a', '.wma'],
      'application/*': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar', '.7z', '.tar', '.gz'],
      'text/*': ['.txt', '.rtf', '.csv'],
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: true,
    noClick: true, // We'll handle click separately
  })

  // Handle browse files button
  const handleBrowseFiles = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  // Handle file input change
  const handleFileInputChange = (event) => {
    const files = Array.from(event.target.files)
    if (files.length > 0) {
      onDrop(files)
    }
    // Reset input
    event.target.value = ''
  }

  // Get file icon based on type
  const getFileIcon = (item) => {
    if (item.type === 'folder') {
      return (
        <div className="w-full h-full bg-blue-100 rounded flex items-center justify-center">
          <File className="h-8 w-8 text-blue-600" />
        </div>
      )
    }

    const mimeType = item.mime_type || ''

    if (mimeType.startsWith('image/')) {
      return <Image className="h-8 w-8 text-green-600" />
    }
    else if (mimeType.startsWith('video/')) {
      return <Video className="h-8 w-8 text-purple-600" />
    }
    else if (mimeType.startsWith('audio/')) {
      return <Music className="h-8 w-8 text-orange-600" />
    }
    else {
      return <FileText className="h-8 w-8 text-gray-600" />
    }
  }

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0)
      return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
  }

  // Handle load more
  const handleLoadMore = async () => {
    if (loadingMore || currentPage >= lastPage)
      return

    setLoadingMore(true)

    try {
      const currentUrl = new URL(window.location)
      const nextPage = currentPage + 1

      // Build parameters for the request
      const params = new URLSearchParams(currentUrl.search)
      params.set('page', nextPage)

      // Determine the correct route
      const isCompanyContext = window.location.pathname.includes('/company/')
      const baseRoute = isCompanyContext ? '/company/media' : '/admin/media'

      const response = await axios.get(`${baseRoute}?${params.toString()}`, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      })

      if (response.data.media && response.data.media.data) {
        // Append new media to existing media
        setAllMediaData(prev => [...prev, ...response.data.media.data])
        setCurrentPage(response.data.media.current_page)
        setLastPage(response.data.media.last_page)

        toast({
          title: 'Success',
          description: `Loaded ${response.data.media.data.length} more files.`,
        })
      }
    }
    catch (error) {
      console.error('Load more error:', error)
      toast({
        title: 'Error',
        description: 'Failed to load more files.',
        variant: 'destructive',
      })
    }
    finally {
      setLoadingMore(false)
    }
  }

  // Handle copy URL
  const handleCopyUrl = async (url) => {
    try {
      await navigator.clipboard.writeText(url)
      toast({
        title: 'Copied',
        description: 'URL copied to clipboard.',
      })
    }
    catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to copy URL.',
        variant: 'destructive',
      })
    }
  }

  // Handle edit save
  const handleEditSave = async () => {
    try {
      const response = await axios.put(`/admin/media/${selectedItem.id}`, editData)

      if (response.data.success) {
        toast({
          title: 'Success',
          description: 'File updated successfully.',
        })

        // Update the selected item with new data
        setSelectedItem(prev => ({
          ...prev,
          ...editData,
        }))

        setIsEditing(false)

        // Trigger refresh if callback provided
        if (onMediaEdit) {
          onMediaEdit(selectedItem, editData)
        }
      }
    }
    catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update file.',
        variant: 'destructive',
      })
    }
  }

  // Handle delete
  const handleDelete = async () => {
    try {
      const response = await axios.delete(`/admin/media/${selectedItem.id}`)

      if (response.data.success) {
        toast({
          title: 'Success',
          description: 'File deleted successfully.',
        })

        setShowDeleteModal(false)
        setSelectedItem(null)

        console.log('handel delete')

        handleRefresh()

        // Refresh the page or trigger callback
        if (onUploadComplete) {
          onUploadComplete()
        }
        else {
          router.reload()
        }
      }
    }
    catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete file.',
        variant: 'destructive',
      })
    }
  }

  // Handle move
  const handleMove = () => {
    if (onMediaMove) {
      onMediaMove(selectedItem)
    }
    else {
      toast({
        title: 'Move',
        description: 'Move functionality will be implemented soon.',
      })
    }
  }

  // Handle folder navigation
  const handleFolderNavigation = (folder) => {
    const isCompanyContext = window.location.pathname.includes('/company/')
    const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index'

    router.get(route(routeName), {
      folder_id: folder.id,
    }, {
      preserveState: true,
      preserveScroll: false,
    })
  }

  // Handle back navigation
  const handleBackNavigation = () => {
    const isCompanyContext = window.location.pathname.includes('/company/')
    const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index'

    const parentId = currentFolder?.parent_id || null

    router.get(route(routeName), {
      folder_id: parentId,
    }, {
      preserveState: true,
      preserveScroll: false,
    })
  }

  return (
    <div className="h-full">
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={supportedExtensions.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />

      <Card className="h-full">
        <CardContent className="p-0 h-full">
          <div className="flex h-full">
            {/* Main Media Area */}
            <div className={cn(
              'flex-1 transition-all duration-300',
              sidebarVisible ? 'w-3/4' : 'w-full',
            )}
            >
              {/* Upload Area with Drag & Drop */}
              <div
                {...getRootProps()}
                className={cn(
                  'h-full relative',
                  isDragActive && 'bg-primary/5 border-primary border-2 border-dashed',
                )}
              >
                <input {...getInputProps()} />

                {/* Upload Overlay */}
                {isDragActive && (
                  <div className="absolute inset-0 bg-primary/10 flex items-center justify-center z-10">
                    <div className="text-center">
                      <Upload className="h-12 w-12 text-primary mx-auto mb-4" />
                      <p className="text-lg font-medium text-primary">
                        Drop files here to upload
                      </p>
                    </div>
                  </div>
                )}

                {/* Upload Progress Overlay */}
                {uploading && (
                  <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-20">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                      <p className="text-sm font-medium mb-2">Uploading files...</p>
                      <div className="w-64 bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        {uploadProgress}
                        %
                      </p>
                    </div>
                  </div>
                )}

                {/* Media Grid */}
                <ScrollArea className="h-[600px]">
                  <div className="p-4">
                    {isRefreshing ? (
                    /* Refresh Loading State */
                      <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                          <p className="text-sm text-muted-foreground">Refreshing files...</p>
                        </div>
                      </div>
                    ) : (
                      <div
                        className={cn(
                          'flex flex-wrap gap-4 justify-start',
                        )}
                      >
                        {/* Back Navigation Card */}
                        {currentFolder && (
                          <Card
                            className="cursor-pointer transition-all hover:shadow-md border-2 w-[150px] h-[150px] border-dashed border-muted-foreground/30 hover:border-primary/50"
                            onDoubleClick={handleBackNavigation}
                          >
                            <CardContent className="p-3 h-full flex flex-col items-center justify-center text-center">
                              <ChevronLeft className="h-12 w-12 text-muted-foreground mb-2" />
                              <p className="text-xs font-medium text-muted-foreground">
                                Back
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Double click
                              </p>
                            </CardContent>
                          </Card>
                        )}

                        {/* Folders */}
                        {folders.map(folder => (
                          <Card
                            key={`folder-${folder.id}`}
                            className={cn(
                              'cursor-pointer transition-all hover:shadow-md border-2 w-[150px] h-[150px]',
                              selectedItem?.id === folder.id && selectedItem?.type === 'folder'
                                ? 'border-primary bg-primary/5'
                                : 'border-transparent hover:border-primary/20',
                            )}
                            onClick={() => handleItemClick(folder, 'folder')}
                            onDoubleClick={() => handleFolderNavigation(folder)}
                          >
                            <CardContent className="p-3 h-full flex flex-col items-center justify-center text-center">
                              <Folder className="h-12 w-12 text-blue-600 mb-2" />
                              <p className="text-xs font-medium truncate w-full" title={folder.name}>
                                {folder.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {folder.media_files_count || 0}
                                {' '}
                                files
                              </p>
                            </CardContent>
                          </Card>
                        ))}

                        {/* Media Files */}
                        {allMediaData?.map(mediaItem => (
                          <Card
                            key={`media-${mediaItem.id}`}
                            className={cn(
                              'cursor-pointer transition-all hover:shadow-md border-2 w-[150px] h-[150px]',
                              selectedItem?.id === mediaItem.id && selectedItem?.type === 'file'
                                ? 'border-primary bg-primary/5'
                                : 'border-transparent hover:border-primary/20',
                            )}
                            onClick={() => handleItemClick(mediaItem, 'file')}
                          >
                            <CardContent className="p-2 h-full flex flex-col">
                              <div className="flex-1 overflow-hidden rounded mb-2">
                                {mediaItem.mime_type?.startsWith('image/')
                                  ? (
                                      <img
                                        src={mediaItem.thumbnail_url || mediaItem.url}
                                        alt={mediaItem.alt_text || mediaItem.original_filename}
                                        className="w-full h-full object-cover"
                                      />
                                    )
                                  : (
                                      <div className="w-full h-full bg-muted flex items-center justify-center">
                                        {getFileIcon(mediaItem)}
                                      </div>
                                    )}
                              </div>
                              <div className="text-center">
                                <p className="text-xs font-medium truncate" title={mediaItem.title || mediaItem.original_filename}>
                                  {mediaItem.title || mediaItem.original_filename}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {formatFileSize(mediaItem.size)}
                                </p>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}

                    {/* Load More Button */}
                    {!isRefreshing && currentPage < lastPage && (
                      <div className="flex justify-center mt-6">
                        <Button
                          variant="outline"
                          onClick={handleLoadMore}
                          disabled={loadingMore}
                        >
                          {loadingMore
                            ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Loading...
                                </>
                              )
                            : (
                                <>
                                  <Plus className="h-4 w-4 mr-2" />
                                  Load More
                                </>
                              )}
                        </Button>
                      </div>
                    )}

                    {/* Empty State */}
                    {(!folders || folders.length === 0) && (!media.data || media.data.length === 0) && (
                      <div className="flex flex-col items-center justify-center h-64 text-center">
                        <Upload className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">No files yet</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Drag and drop files here or use the upload button
                        </p>
                        <Button onClick={handleBrowseFiles}>
                          <Upload className="h-4 w-4 mr-2" />
                          Browse Files
                        </Button>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>

            {/* Right Sidebar - Integrated */}
            <div className={cn(
              'transition-all duration-300 border-l border-border bg-muted/30',
              sidebarVisible ? 'w-1/4 min-w-64' : 'w-0 overflow-hidden',
            )}
            >
              <div className="h-full flex flex-col">
                {/* Sidebar Header */}
                <div className="p-4 border-b border-border flex items-center justify-between bg-background">
                  <h3 className="font-semibold">File Details</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSidebarVisible(false)}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                {/* Sidebar Content */}
                <ScrollArea className="flex-1 h-[calc(600px-60px)]">
                  <div className="p-4 space-y-6">
                    {selectedItem ? (
                      <>
                        <div className="space-y-3">
                          {isEditing ? (
                          /* Edit Form */
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label htmlFor="edit-title">Title</Label>
                                <Input
                                  id="edit-title"
                                  value={editData.title}
                                  onChange={e => setEditData(prev => ({ ...prev, title: e.target.value }))}
                                  placeholder="Enter title"
                                />
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="edit-alt">Alt Text</Label>
                                <Input
                                  id="edit-alt"
                                  value={editData.alt_text}
                                  onChange={e => setEditData(prev => ({ ...prev, alt_text: e.target.value }))}
                                  placeholder="Enter alt text"
                                />
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="edit-description">Description</Label>
                                <Textarea
                                  id="edit-description"
                                  value={editData.description}
                                  onChange={e => setEditData(prev => ({ ...prev, description: e.target.value }))}
                                  placeholder="Enter description"
                                  rows={3}
                                />
                              </div>

                              <div className="grid grid-cols-2 gap-2">
                                <Button onClick={handleEditSave} size="sm">
                                  Save Changes
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setIsEditing(false)}
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                          /* Action Buttons */
                            <div className="grid grid-cols-3 gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full cursor-pointer"
                                onClick={() => setIsEditing(true)}
                              >
                                <Edit3 className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full cursor-pointer text-destructive hover:text-destructive"
                                onClick={() => setShowDeleteModal(true)}
                              >
                                <Trash2 className="h-3 w-3 mr-1" />
                                Delete
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full cursor-pointer"
                                onClick={handleMove}
                              >
                                <FolderOpen className="h-3 w-3 mr-1" />
                                Move
                              </Button>
                            </div>
                          )}
                        </div>

                        {/* File Preview */}
                        <div className="space-y-4">
                          <div className="aspect-square bg-muted rounded-lg overflow-hidden">
                            {selectedItem.type === 'file' && selectedItem.mime_type?.startsWith('image/')
                              ? (
                                  <img
                                    src={selectedItem.url}
                                    alt={selectedItem.alt_text || selectedItem.original_filename}
                                    className="w-full h-full object-contain"
                                  />
                                )
                              : selectedItem.type === 'folder'
                                ? (
                                    <div className="w-full h-full flex items-center justify-center">
                                      <Folder className="h-16 w-16 text-blue-600" />
                                    </div>
                                  )
                                : (
                                    <div className="w-full h-full flex items-center justify-center">
                                      {getFileIcon(selectedItem)}
                                    </div>
                                  )}
                          </div>
                        </div>

                        {/* File Information */}
                        <div className="space-y-4">
                          {/* File Name */}
                          <div>
                            <Label className="text-sm font-medium">File Name</Label>
                            <p className="text-sm text-muted-foreground mt-1">
                              {selectedItem.title || selectedItem.original_filename || selectedItem.name}
                            </p>
                          </div>

                          {/* File Path with Copy */}
                          {selectedItem.type === 'file' && (
                            <div>
                              <Label className="text-sm font-medium">File Path</Label>
                              <div className="flex items-center gap-2 mt-1">
                                <Input
                                  value={selectedItem.url}
                                  readOnly
                                  className="text-xs"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCopyUrl(selectedItem.url)}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          )}

                          {/* Uploaded By */}
                          {selectedItem.uploader && (
                            <div>
                              <Label className="text-sm font-medium">Uploaded by</Label>
                              <p className="text-sm text-muted-foreground mt-1">
                                {selectedItem.uploader.name}
                              </p>
                            </div>
                          )}

                          {/* File Size */}
                          {selectedItem.type === 'file' && selectedItem.size && (
                            <div>
                              <Label className="text-sm font-medium">File Size</Label>
                              <p className="text-sm text-muted-foreground mt-1">
                                {formatFileSize(selectedItem.size)}
                              </p>
                            </div>
                          )}

                          {/* Image Dimensions */}
                          {selectedItem.type === 'file' && selectedItem.width && selectedItem.height && (
                            <>
                              <div>
                                <Label className="text-sm font-medium">Width</Label>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {selectedItem.width}
                                  {' '}
                                  pixels
                                </p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium">Height</Label>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {selectedItem.height}
                                  {' '}
                                  pixels
                                </p>
                              </div>
                            </>
                          )}

                          {/* Uploaded At */}
                          {selectedItem.created_at && (
                            <div>
                              <Label className="text-sm font-medium">Uploaded at</Label>
                              <p className="text-sm text-muted-foreground mt-1">
                                {new Date(selectedItem.created_at).toLocaleString()}
                              </p>
                            </div>
                          )}
                        </div>

                      </>
                    ) : (
                      <div className="text-center text-muted-foreground py-8">
                        <File className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <h4 className="font-medium mb-2">No file selected</h4>
                        <p className="text-sm">Click on any file or folder to view details and information</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete File</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "
              {selectedItem?.title || selectedItem?.original_filename || selectedItem?.name}
              "?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
