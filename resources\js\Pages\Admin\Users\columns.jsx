'use client'

import { Link, router } from '@inertiajs/react'
import { Edit, Eye, MoreHorizontal, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import { route } from 'ziggy-js'
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/shadcn/ui/avatar'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Checkbox } from '@/Components/shadcn/ui/checkbox'
import { ConfirmDialog } from '@/Components/shadcn/ui/confirm-dialog'
import { DataTableColumnHeader } from '@/Components/shadcn/ui/data-table-column-header'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/Components/shadcn/ui/dropdown-menu'

export const columns = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected()
          || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="User" />
    ),
    cell: ({ row }) => {
      const user = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.profile_photo_url} alt={user.name} />
            <AvatarFallback>
              {user.name?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: 'user_type',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="User Type" />
    ),
    cell: ({ row }) => {
      const userType = row.getValue('user_type')
      const getVariant = (type) => {
        switch (type?.toLowerCase()) {
          case 'platform_administrator':
            return 'destructive'
          case 'brokerage_admin':
            return 'default'
          case 'independent_agent':
            return 'secondary'
          case 'company_agent':
            return 'outline'
          default:
            return 'outline'
        }
      }

      return (
        <Badge variant={getVariant(userType)}>
          {userType?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown'}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'email_status',
    header: 'Email Status',
    cell: ({ row }) => {
      const status = row.getValue('email_status')
      return status === 'Verified'
        ? (
            <Badge variant="success">Verified</Badge>
          )
        : (
            <Badge variant="secondary">Unverified</Badge>
          )
    },
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('created_at'))
      return (
        <div className="text-sm">
          {date.toLocaleDateString()}
        </div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const user = row.original
      const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

      const handleDelete = () => {
        router.delete(route('admin.users.destroy', user.id), {
          onSuccess: () => {
            toast.success('User deleted successfully')
            window.location.reload() // Refresh the page to update data
          },
          onError: () => {
            toast.error('Failed to delete user')
          },
        })
      }

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">

              <DropdownMenuItem asChild>
                <Link href={route('admin.users.show', user.id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View user
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={route('admin.users.edit', user.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit user
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => setShowDeleteConfirm(true)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete user
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <ConfirmDialog
            isOpen={showDeleteConfirm}
            onClose={() => setShowDeleteConfirm(false)}
            onConfirm={handleDelete}
            title="Delete User"
            description={`Are you sure you want to delete "${user.name}"? This action cannot be undone.`}
            confirmText="Delete"
            cancelText="Cancel"
            variant="destructive"
          />
        </>
      )
    },
  },
]
