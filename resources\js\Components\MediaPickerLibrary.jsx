import {
  File,
  FileText,
  Folder,
  Image,
  Loader2,
  Music,
  Plus,
  Video,
} from 'lucide-react'
import { useState } from 'react'
import { cn } from '@/Components/lib/utils'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent } from '@/Components/shadcn/ui/card'
import { ScrollArea } from '@/Components/shadcn/ui/scroll-area'

export default function MediaPickerLibrary({
  folders = [],
  media = [],
  selectedMedia,
  onMediaSelect,
  onFolderClick,
  loading = false,
  onLoadMore,
  hasMore = false,
}) {
  const [loadingMore, setLoadingMore] = useState(false)

  // Get file icon based on type
  const getFileIcon = (item) => {
    if (item.type === 'folder') {
      return <Folder className="h-8 w-8 text-blue-600" />
    }

    const mimeType = item.mime_type || ''

    if (mimeType.startsWith('image/')) {
      return <Image className="h-8 w-8 text-green-600" />
    }
    else if (mimeType.startsWith('video/')) {
      return <Video className="h-8 w-8 text-purple-600" />
    }
    else if (mimeType.startsWith('audio/')) {
      return <Music className="h-8 w-8 text-orange-600" />
    }
    else {
      return <FileText className="h-8 w-8 text-gray-600" />
    }
  }

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0)
      return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
  }

  // Handle load more
  const handleLoadMore = async () => {
    if (loadingMore || !hasMore)
      return

    setLoadingMore(true)
    try {
      await onLoadMore()
    }
    finally {
      setLoadingMore(false)
    }
  }

  // Handle item click
  const handleItemClick = (item) => {
    if (item.type === 'folder') {
      onFolderClick(item)
    }
    else {
      onMediaSelect(item)
    }
  }

  return (
    <Card className="h-full">
      <CardContent className="p-0 h-full">
        <ScrollArea className="h-[400px]">
          <div className="p-4">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                  {/* Folders */}
                  {folders.map(folder => (
                    <Card
                      key={`folder-${folder.id}`}
                      className="cursor-pointer transition-all hover:shadow-md"
                      onClick={() => handleItemClick(folder)}
                    >
                      <CardContent className="p-3">
                        <div className="aspect-square mb-2 flex items-center justify-center bg-blue-50 rounded">
                          <Folder className="h-8 w-8 text-blue-600" />
                        </div>
                        <p className="text-xs font-medium truncate" title={folder.name}>
                          {folder.name}
                        </p>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Media Files */}
                  {media.data?.map(mediaItem => (
                    <Card
                      key={`media-${mediaItem.id}`}
                      className={cn(
                        'cursor-pointer transition-all hover:shadow-md',
                        selectedMedia?.id === mediaItem.id && 'ring-2 ring-primary',
                      )}
                      onClick={() => handleItemClick(mediaItem)}
                    >
                      <CardContent className="p-3">
                        <div className="aspect-square mb-2 overflow-hidden rounded">
                          {mediaItem.mime_type?.startsWith('image/')
                            ? (
                                <img
                                  src={mediaItem.thumbnail_url || mediaItem.url}
                                  alt={mediaItem.alt_text || mediaItem.original_filename}
                                  className="w-full h-full object-cover"
                                />
                              )
                            : (
                                <div className="w-full h-full bg-muted flex items-center justify-center">
                                  {getFileIcon(mediaItem)}
                                </div>
                              )}
                        </div>
                        <p className="text-xs font-medium truncate" title={mediaItem.title || mediaItem.original_filename}>
                          {mediaItem.title || mediaItem.original_filename}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(mediaItem.size)}
                        </p>

                        {/* Selection indicator */}
                        {selectedMedia?.id === mediaItem.id && (
                          <Badge variant="default" className="mt-1 text-xs">
                            Selected
                          </Badge>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Load More Button */}
                {hasMore && (
                  <div className="flex justify-center mt-6">
                    <Button
                      variant="outline"
                      onClick={handleLoadMore}
                      disabled={loadingMore}
                    >
                      {loadingMore
                        ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Loading...
                            </>
                          )
                        : (
                            <>
                              <Plus className="h-4 w-4 mr-2" />
                              Load More
                            </>
                          )}
                    </Button>
                  </div>
                )}

                {/* Empty State */}
                {(!folders || folders.length === 0) && (!media.data || media.data.length === 0) && (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <File className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No files found</h3>
                    <p className="text-sm text-muted-foreground">
                      This folder is empty or no files match your search.
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
