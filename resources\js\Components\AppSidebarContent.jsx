import { Icon } from '@iconify/react'
import { Link, usePage } from '@inertiajs/react'
import { useEffect, useState } from 'react'
import { route } from 'ziggy-js'
import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/Components/shadcn/ui/sidebar'

const navigationConfig = [
  {
    label: 'Platform',
    items: [
      { name: 'Dashboard', icon: 'lucide:layout-dashboard', route: 'dashboard', isActive: true },
      { name: 'Users', icon: 'lucide:users', route: 'admin.users.index', adminOnly: true },
      { name: 'Plans', icon: 'lucide:credit-card', route: 'admin.plans.index', adminOnly: true },
      { name: 'Media', icon: 'lucide:image', route: 'admin.media.index', adminOnly: true },
      { name: 'Billing', icon: 'lucide:credit-card', route: 'subscriptions.create' },
      { name: 'Settings', icon: 'lucide:settings', route: 'admin.settings.index', adminOnly: true },
      { name: 'Chat', icon: 'lucide:message-circle', route: 'chat.index' },
    ],
  },
  {
    label: 'Company',
    items: [
      { name: 'Agents', icon: 'lucide:users', route: 'company.agents.index', companyOnly: true },
      { name: 'Media', icon: 'lucide:image', route: 'company.media.index', companyOnly: true },
    ],
  },
  {
    label: 'API',
    items: [
      { name: 'API Tokens', icon: 'lucide:key', route: 'api-tokens.index' },
      { name: 'API Documentation', icon: 'lucide:book-heart', route: 'scribe', external: true },
    ],
  },
  {
    label: null,
    class: 'mt-auto',
    items: [
      {
        name: 'Support',
        icon: 'lucide:life-buoy',
        href: 'https://github.com/shipfastlabs/larasonic-react/issues',
        external: true,
      },
      {
        name: 'Documentation',
        icon: 'lucide:book-marked',
        href: 'https://docs.larasonic.com',
        external: true,
      },
    ],
  },
]

export default function AppSidebarContent() {
  const [theme, setTheme] = useState(() => {
    if (typeof window !== 'undefined') {
      // Check localStorage first
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme) {
        return savedTheme
      }
      // Check system preference
      if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark'
      }
    }
    return 'light'
  })

  const { props } = usePage()
  const user = props.auth?.user

  useEffect(() => {
    // Apply theme to document
    document.documentElement.classList.toggle('dark', theme === 'dark')
    // Save to localStorage
    localStorage.setItem('theme', theme)
  }, [theme])

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      // Only update if no theme is saved in localStorage
      if (!localStorage.getItem('theme')) {
        setTheme(mediaQuery.matches ? 'dark' : 'light')
      }
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  // Check user permissions
  const isAdmin = user?.can_access_admin || false
  const isBrokerageAdmin = user?.user_type === 'brokerage_admin' || false
  const canUploadMedia = user?.can_upload_media || false

  const renderLink = (item) => {
    if (item.external) {
      return {
        Component: 'a',
        props: {
          href: item.href || route(item.route),
          target: '_blank',
          rel: 'noopener noreferrer',
        },
      }
    }
    return {
      Component: Link,
      props: {
        href: route(item.route),
      },
    }
  }

  return (
    <SidebarContent>
      {navigationConfig.map((group, index) => (
        <SidebarGroup key={group.label} className={group.class}>
          {group.label && <SidebarGroupLabel>{group.label}</SidebarGroupLabel>}
          <SidebarMenu>
            {group.items
              .filter((item) => {
                // Filter admin-only items (only show to Super Admin)
                if (item.adminOnly && !isAdmin)
                  return false
                // Filter company-only items (hide from Super Admin, show to Company Admin and Agents)
                if (item.companyOnly && isAdmin)
                  return false
                if (item.companyOnly && !isBrokerageAdmin && !canUploadMedia)
                  return false
                return true
              })
              .map((item) => {
                const { Component, props } = renderLink(item)
                return (
                  <SidebarMenuItem
                    key={item.name}
                    active={!item.external && route().current(item.route)}
                  >
                    <SidebarMenuButton asChild isActive={!item.external && route().current(item.route)}>
                      <Component {...props}>
                        <Icon icon={item.icon} className="h-4 w-4" />
                        {item.name}
                      </Component>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            {index === navigationConfig.length - 1 && (
              <SidebarMenuItem>
                <SidebarMenuButton onClick={toggleTheme}>
                  <Icon icon={theme === 'dark' ? 'lucide:sun' : 'lucide:moon'} className="mr-2" />
                  {theme === 'dark' ? 'Light' : 'Dark'}
                  {' '}
                  Mode
                </SidebarMenuButton>
              </SidebarMenuItem>
            )}
          </SidebarMenu>
        </SidebarGroup>
      ))}
    </SidebarContent>
  )
}
