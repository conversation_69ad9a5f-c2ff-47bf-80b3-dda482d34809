<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Inertia\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

final class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        /** @var array<string, mixed> */
        return array_merge(parent::share($request), [
            'name' => Config::get('app.name', 'Larasonic'),
            'auth' => [
                'user' => $request->user() ? array_merge(
                    $request->user()->load('profileImage')->toArray(),
                    [
                        'can_access_admin' => $request->user()->canAccessAdmin(),
                    ]
                ) : null,
            ],
            'companyBranding' => $this->getCompanyBranding($request),
            'flash' => [
                'message' => fn () => $request->session()->get('message'),
                'success' => fn () => $request->session()->get('success'),
                'error' => fn () => $request->session()->get('error'),
            ],
        ]);
    }

    /**
     * Get company branding settings based on user type.
     */
    private function getCompanyBranding(Request $request): ?array
    {
        $user = $request->user();

        if (!$user) {
            return null;
        }

        // For platform administrators - no custom branding
        if ($user->user_type->value === 'platform_administrator') {
            return [
                'company_name' => 'MLS Platform',
                'logo_url' => null,
                'brand_color' => '#3b82f6', // Default blue
            ];
        }

        // For brokerage admins and company agents - use company branding
        if (in_array($user->user_type->value, ['brokerage_admin', 'company_agent']) && $user->currentTeam) {
            return [
                'company_name' => $user->currentTeam->name,
                'logo_url' => null, // TODO: Add company logo field to teams table
                'brand_color' => '#10b981', // Default green for companies
            ];
        }

        // For independent agents - use personal branding
        if ($user->user_type->value === 'independent_agent') {
            return [
                'business_name' => $user->name . ' Real Estate',
                'logo_url' => null, // TODO: Add personal logo field to users table
                'brand_color' => '#8b5cf6', // Default purple for independent agents
            ];
        }

        return null;
    }
}
