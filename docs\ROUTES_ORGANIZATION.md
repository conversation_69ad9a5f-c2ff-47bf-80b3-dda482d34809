# Routes Organization

## Overview

The routes have been reorganized into separate files based on user roles for better maintainability and security. Each role has its own dedicated route file with appropriate middleware protection.

## Route Files Structure

```
routes/
├── web.php           # Common routes (auth, dashboard, subscriptions)
├── platform.php      # Platform Administrator routes
├── brokerage.php      # Brokerage Admin routes
└── agents.php         # Independent & Company Agent routes
```

## Role-Based Route Organization

### 1. Platform Administrator Routes (`routes/platform.php`)
**Middleware**: `role:platform_administrator`  
**Prefix**: `/admin`  
**Name Prefix**: `admin.`

**Available Routes**:
- **User Management**: CRUD operations for all users
- **Plan Management**: Subscription plan management
- **Media Management**: Platform-level media control
- **Platform Settings**: System-wide configuration
  - General settings
  - Appearance settings
  - Notification settings
  - Security settings

**Example URLs**:
- `/admin/users` - User management
- `/admin/settings` - Platform settings
- `/admin/media` - Media management

### 2. Brokerage Admin Routes (`routes/brokerage.php`)
**Middleware**: `role:brokerage_admin`  
**Prefix**: `/company`  
**Name Prefix**: `company.`

**Available Routes**:
- **Agent Management**: Manage company agents
- **Company Media**: Company-scoped media management
- **Company Settings**: Company branding and preferences
  - Company branding
  - Company preferences

**Example URLs**:
- `/company/agents` - Agent management
- `/company/settings` - Company settings
- `/company/media` - Company media

### 3. Independent Agent Routes (`routes/agents.php`)
**Middleware**: `role:independent_agent`  
**Prefix**: `/agent`  
**Name Prefix**: `agent.`

**Available Routes**:
- **Agent Settings**: Personal branding and preferences
  - Personal branding (full access)
  - Personal preferences

**Example URLs**:
- `/agent/settings` - Agent settings
- `/agent/settings/branding` - Personal branding

### 4. Company Agent Routes (`routes/agents.php`)
**Middleware**: `role:company_agent`  
**Prefix**: `/company-agent`  
**Name Prefix**: `company-agent.`

**Available Routes**:
- **Agent Settings**: Limited personal settings
  - Personal preferences only
  - No branding (inherits from company)

**Example URLs**:
- `/company-agent/settings` - Agent settings
- `/company-agent/settings/preferences` - Personal preferences

## Access Control Summary

| Role | User Management | Company Management | Agent Management | Platform Settings | Company Settings | Personal Branding |
|------|----------------|-------------------|------------------|-------------------|------------------|-------------------|
| Platform Administrator | ✅ All Users | ✅ All Companies | ✅ All Agents | ✅ Full Access | ✅ All Companies | ❌ N/A |
| Brokerage Admin | ✅ Company Users | ✅ Own Company | ✅ Company Agents | ❌ No Access | ✅ Own Company | ❌ N/A |
| Independent Agent | ❌ No Access | ❌ No Access | ❌ No Access | ❌ No Access | ❌ No Access | ✅ Full Access |
| Company Agent | ❌ No Access | ❌ No Access | ❌ No Access | ❌ No Access | ❌ No Access | ❌ Inherits from Company |

## Middleware Configuration

The following middleware are configured in `bootstrap/app.php`:

```php
$middleware->alias([
    'role' => \App\Http\Middleware\RoleBasedAccess::class,
    'company.scope' => \App\Http\Middleware\CompanyScope::class,
    'platform.admin' => \App\Http\Middleware\EnsurePlatformAdmin::class,
]);
```

## Route Loading

Routes are automatically loaded in `bootstrap/app.php`:

```php
->withRouting(
    web: __DIR__.'/../routes/web.php',
    api: __DIR__.'/../routes/api.php',
    health: '/up',
    then: function () {
        Route::middleware('web')->group(base_path('routes/platform.php'));
        Route::middleware('web')->group(base_path('routes/brokerage.php'));
        Route::middleware('web')->group(base_path('routes/agents.php'));
    },
)
```

## Security Features

1. **Role-based Middleware**: Each route group is protected by appropriate role middleware
2. **Enum-based Validation**: Uses UserType enum for type-safe role checking
3. **Hierarchical Access**: Platform Admin can access everything, others are scoped
4. **Company Scoping**: Brokerage admins are limited to their company scope
5. **Permission Gates**: Additional gate-based permissions for fine-grained control

## Testing Access

Test users have been created with the following credentials:

- **Platform Administrator**: `<EMAIL>`
- **Brokerage Admin**: `<EMAIL>`
- **Independent Agent**: `<EMAIL>`
- **Company Agent**: `<EMAIL>`

All test users have the password: `password`
