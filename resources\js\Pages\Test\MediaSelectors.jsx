import { useState } from 'react'
import MediaFileSelector from '@/Components/MediaFileSelector'
import MediaImageSelector from '@/Components/MediaImageSelector'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import AppLayout from '@/Layouts/AppLayout'

export default function MediaSelectors() {
  const [selectedImage, setSelectedImage] = useState(null)
  const [selectedImageMedia, setSelectedImageMedia] = useState(null)
  const [selectedFile, setSelectedFile] = useState(null)
  const [selectedFileMedia, setSelectedFileMedia] = useState(null)

  const handleImageSelect = (mediaId, mediaObject = null) => {
    setSelectedImage(mediaId)
    setSelectedImageMedia(mediaObject)
    console.log('Image selected:', { mediaId, mediaObject })
  }

  const handleFileSelect = (mediaId, mediaObject = null) => {
    setSelectedFile(mediaId)
    setSelectedFileMedia(mediaObject)
    console.log('File selected:', { mediaId, mediaObject })
  }

  return (
    <AppLayout
      title="Media Selectors Test"
      renderHeader={() => (
        <h2 className="font-semibold text-xl text-gray-800 leading-tight">
          Media Selectors Test
        </h2>
      )}
    >
      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

            {/* MediaImageSelector Demo */}
            <Card>
              <CardHeader>
                <CardTitle>MediaImageSelector</CardTitle>
                <CardDescription>
                  Square 150x150px card for image selection with thumbnail preview
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <MediaImageSelector
                  label="Profile Image"
                  value={selectedImage}
                  onChange={handleImageSelect}
                  selectedMedia={selectedImageMedia}
                  accept="image/*"
                  placeholder="Select an image"
                  description="Choose a profile image from your media library"
                />

                {/* Debug Info */}
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Debug Info:</h4>
                  <p className="text-xs text-gray-600">
                    <strong>Selected ID:</strong>
                    {' '}
                    {selectedImage || 'None'}
                  </p>
                  <p className="text-xs text-gray-600">
                    <strong>Media Object:</strong>
                    {' '}
                    {selectedImageMedia ? 'Available' : 'None'}
                  </p>
                  {selectedImageMedia && (
                    <p className="text-xs text-gray-600">
                      <strong>Filename:</strong>
                      {' '}
                      {selectedImageMedia.original_filename}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* MediaFileSelector Demo */}
            <Card>
              <CardHeader>
                <CardTitle>MediaFileSelector</CardTitle>
                <CardDescription>
                  Rectangular card for file selection with file icon, name, and size
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <MediaFileSelector
                  label="Document File"
                  value={selectedFile}
                  onChange={handleFileSelect}
                  selectedMedia={selectedFileMedia}
                  accept="all"
                  placeholder="Select a file"
                  description="Choose any file from your media library"
                />

                {/* Debug Info */}
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Debug Info:</h4>
                  <p className="text-xs text-gray-600">
                    <strong>Selected ID:</strong>
                    {' '}
                    {selectedFile || 'None'}
                  </p>
                  <p className="text-xs text-gray-600">
                    <strong>Media Object:</strong>
                    {' '}
                    {selectedFileMedia ? 'Available' : 'None'}
                  </p>
                  {selectedFileMedia && (
                    <>
                      <p className="text-xs text-gray-600">
                        <strong>Filename:</strong>
                        {' '}
                        {selectedFileMedia.original_filename}
                      </p>
                      <p className="text-xs text-gray-600">
                        <strong>File Type:</strong>
                        {' '}
                        {selectedFileMedia.file_type}
                      </p>
                      <p className="text-xs text-gray-600">
                        <strong>Size:</strong>
                        {' '}
                        {selectedFileMedia.formatted_size}
                      </p>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

          </div>

          {/* Usage Examples */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Usage Examples</CardTitle>
              <CardDescription>
                How to use these components in your forms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">MediaImageSelector</h4>
                  <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                    {`<MediaImageSelector
  label="Profile Image"
  value={form.data.profile_image_id}
  onChange={handleImageSelect}
  selectedMedia={selectedImageMedia}
  accept="image/*"
  placeholder="Select an image"
  required
/>`}
                  </pre>
                </div>
                <div>
                  <h4 className="font-medium mb-2">MediaFileSelector</h4>
                  <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                    {`<MediaFileSelector
  label="Document"
  value={form.data.document_id}
  onChange={handleFileSelect}
  selectedMedia={selectedFileMedia}
  accept="all"
  placeholder="Select a file"
  required
/>`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
